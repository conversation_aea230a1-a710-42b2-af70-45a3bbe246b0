﻿using AIMaaS.Models;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
using Dapper;
using Google.Cloud.Firestore;
using Google.Cloud.Storage.V1;
using Google.Type;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using OrderTracking.API.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;


namespace AIMaaS.Services
{
    public class AnteaService : IAnteaService
    {
        private readonly IOptions<AnteaData> _dataOptions;
        private readonly FirestoreDb _firestoreDb1;
        private readonly IEmailService _emails;
        private List<AssetManagementSites> _assetManagementSites1;
        private readonly String _anteaSubmissionsBucketName;
        private List<EquipmentData> _equipmentDataList;
        private static readonly SemaphoreSlim _initializationSemaphore = new SemaphoreSlim(1, 1);
        private static bool _isInitialized = false;

        public AnteaService(IOptions<AnteaData> dataOptions, IOptions<Connections> options, IServiceProvider serviceProvider)
        {

            FirestoreDb firestoreDb = new FirestoreDbBuilder
            {
                ProjectId = options.Value.ProjectId,
                DatabaseId = options.Value.DatabaseName
            }.Build();
            _firestoreDb1 = firestoreDb;
            _dataOptions = dataOptions;
            _anteaSubmissionsBucketName = options.Value.AnteaSubmissionsBucketName;
            InitializeAsync().Wait();
        }
        private async Task InitializeAsync()
        {
            _assetManagementSites1 = await GetAllAssetManagementSitesAsync();
            await _initializationSemaphore.WaitAsync();
            try
            {
                if (!_isInitialized)
                {

                    _equipmentDataList = await GetEquipmentData();
                    _isInitialized = true;
                }
            }
            finally
            {
                _initializationSemaphore.Release();
            }
        }

        public async Task<List<AnteaAsset>> GetAssetsAsync()
        {

            await InitializeAsync();
            string ids = string.Join(", ", _equipmentDataList
                                 .SelectMany(ed => ed.EquipmentList)
                                 .Select(s => $"'{s.ID}'"));
            var assetsList = await GetAssestsByIds(ids);

            var mappedAssets = (from asset in assetsList
                                from equipmentData in _equipmentDataList
                                from equipment in equipmentData.EquipmentList
                                where asset.ID == equipment.ID


                                select new AnteaAsset
                                {
                                    CLIENTID = equipmentData.ClientID,
                                    LINK = asset.LINK,
                                    IMAGE = asset.IMAGE,
                                    CLIENTNAME = equipmentData.ClientName,
                                    LOCATIONID = equipmentData.LocationID,
                                    LOCATIONNAME = equipmentData.LocationName,
                                    ASSETID = asset.ASSETID,
                                    ID = asset.ID,
                                    AREAID = asset.AREAID,
                                    AREANAME = asset.AREANAME,
                                    DESCRIPTION = asset.DESCRIPTION,
                                    SERVICE = asset.SERVICE,
                                    EQUIPCATEGORY = asset.EQUIPCATEGORY,
                                    ASSETTYPE = asset.ASSETTYPE,
                                    ASSETCATEGORY = asset.ASSETCATEGORY,
                                    ORIENTATION = asset.ORIENTATION,
                                    CONSTRUCTIONCODE = asset.CONSTRUCTIONCODE,
                                    INSPECTIONCODE = asset.INSPECTIONCODE,
                                    MANUFACTURER = asset.MANUFACTURER,
                                    CONSTRUCTIONYEAR = asset.CONSTRUCTIONYEAR,
                                    DIMENSIONS = asset.DIMENSIONS,
                                    SERIALNUMBER = asset.SERIALNUMBER,
                                    NATIONALBOARD = asset.NATIONALBOARD,
                                    LOCALJURIDICTIONAL = asset.LOCALJURIDICTIONAL,
                                    PID = asset.PID,
                                    RiskClass = asset.RiskClass,
                                    SCLASS = asset.SCLASS,
                                    CMMS=asset.CMMS

                                }).ToList();
            return mappedAssets;

        }
        public async Task<List<Inspections>> GetInspectionsAsync()
        {
            await InitializeAsync();
            string ids = string.Join(", ", _equipmentDataList
                                 .SelectMany(ed => ed.EquipmentList)
                                 .Select(s => $"'{s.ID}'"));
            var inspectionsList = await GetInspections(ids);
            var mappedInspections = (from inspection in inspectionsList
                                     from equipmentData in _equipmentDataList
                                     from equipment in equipmentData.EquipmentList
                                     where inspection.ASSETID == equipment.ID
                                     select new Inspections
                                     {
                                         // CLIENTID = equipmentData.ClientID,
                                         //   CLIENTNAME = equipmentData.ClientName,
                                         LOCATIONID = equipmentData.LocationID,
                                         LOCATIONNAME = equipmentData.LocationName,
                                         ASSETID = equipment.ID,
                                         ASSETDESCRIPTION = inspection.ASSETDESCRIPTION,
                                         ASSETIDNAME = inspection.ASSETIDNAME,
                                         LINENUMBER = inspection.LINENUMBER,
                                         OPERATIONORSCHEDULE = inspection.OPERATIONORSCHEDULE,
                                         INSPECTIONASSETCATEGORY = inspection.INSPECTIONASSETCATEGORY,
                                         AREA = inspection.AREA,
                                         RISKCLASS = inspection.RISKCLASS,
                                         INSPECTIONTYPE = inspection.INSPECTIONTYPE,
                                         COMPLETEDINSPECTIONDUE = inspection.COMPLETEDINSPECTIONDUE,
                                         INSPECTIONDATE = inspection.INSPECTIONDATE,
                                         RESULT = inspection.RESULT,
                                         INSPECTIONSTATE = inspection.INSPECTIONSTATE,
                                         NEXTINSPECTIONDUE = inspection.NEXTINSPECTIONDUE,
                                         SCHEDULETYPE = inspection.SCHEDULETYPE,
                                         FREQUENCY = inspection.FREQUENCY,
                                         SCHEDULESTATUS = inspection.SCHEDULESTATUS,
                                         NOTES = inspection.NOTES,
                                         PERFORMER = inspection.PERFORMER,
                                         ASSETMANAGEMENTCATEGORY = inspection.ASSETMANAGEMENTCATEGORY,
                                         SCHEDULEID = inspection.SCHEDULEID,
                                         PLANOPERATIONID = inspection.PLANOPERATIONID,
                                         INSPECTIONID = inspection.SCHEDULEID,
                                         DATE = inspection.INSPECTIONDATE,
                                         NEXTDATE = inspection.NEXTINSPECTIONDUE,
                                         LASTDATE = inspection.INSPECTIONDATE,
                                         INSPECTIONDUE = inspection.NEXTINSPECTIONDUE,
                                         OPERATIONTYPE = inspection.INSPECTIONTYPE,
                                         INSPECTIONSTATUS = inspection.INSPECTIONSTATUS,
                                         ASSETSTATUS = inspection.ASSETSTATUS,
                                         NEXTDUEDATENOTES = inspection.NEXTDUEDATENOTES
                                     }).ToList();            
            return mappedInspections;

        }
        public async Task<List<AssetAttachments>> GetAssetAttachmentsAsync(string assetId)
        {

            var assetAttachmentsList = await GetAssetAttachments(assetId);

            return assetAttachmentsList;
        }
        public async Task<List<InspectionAttachments>> GetInspectionAttachmentsAsync(string operationid)
        {


            var inspectionAttachmentsList = await GetInspectionAttachments(operationid);

            return inspectionAttachmentsList;
        }
        public async Task<List<AssetComponents>> GetAssetComponentsAsync(string assetId)
        {

            List<AssetComponents> assetComponents = new List<AssetComponents>();
            var _assetComponents = new List<AssetComponents>();
            var tempassetComponents = await GetAssetComponentDetails(assetId);
            _assetComponents = tempassetComponents.GroupBy(o => o.COMPONENTID).Select(g => g.First()).ToList();
            assetComponents.AddRange(_assetComponents);
            return assetComponents;

        }
        public async Task<List<ChamberData>> GetChamberDataAsync(string assetId)
        {

            List<ChamberData> chamberData = new List<ChamberData>();
            var _chamberData = new List<ChamberData>();
            _chamberData = await GetChamberDataDetails(assetId);
            chamberData.AddRange(_chamberData);
            return chamberData;

        }
        private async Task<List<ChamberData>> GetChamberDataDetails(string assetIds)
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);

            await connection.OpenAsync();
            var assetChambers = await connection.QueryAsync<ChamberData>($@"
                        SELECT 
                            CASE
                                WHEN BOM_PLACE.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy' THEN BOM_PLACE.NAME
                                ELSE BOM_COMP.NAME
                            END AS NAME,
                            CASE
                                WHEN BOM_PLACE.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy' THEN BOM_PLACE_LINE.DESIGN_PRESSURE_T
                                ELSE CONCAT(SUBSTRING(BOM_COMP_EQUIP_CHAMBER.DESIGN_PRESSURE_T, CHARINDEX('\', BOM_COMP_EQUIP_CHAMBER.DESIGN_PRESSURE_T) + 1, CHARINDEX('\', BOM_COMP_EQUIP_CHAMBER.DESIGN_PRESSURE_T, CHARINDEX('\', BOM_COMP_EQUIP_CHAMBER.DESIGN_PRESSURE_T) + 1) - CHARINDEX('\', BOM_COMP_EQUIP_CHAMBER.DESIGN_PRESSURE_T) - 1), ' psi')
                            END AS DESIGNPRESSURE,
                            CASE
                                WHEN BOM_PLACE.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy' THEN BOM_PLACE_LINE.VACUUM_
                                ELSE BOM_COMP_EQUIP_CHAMBER.VACUUM_
                            END AS FULLVACCUM,
                            CASE
                                WHEN BOM_PLACE.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy' THEN BOM_PLACE_LINE.DESIGNTEMPRANGE_TEXTUAL
                                ELSE REPLACE(REPLACE(BOM_COMP_EQUIP_CHAMBER.DESIGN_TEMP_MIN_T, '&deg;', '°'), '\', '')
                            END AS MINTEMP,
                            CASE
                                WHEN BOM_PLACE.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy' THEN BOM_PLACE_LINE.DESIGNTEMPRANGE_TEXTUAL
                                ELSE REPLACE(REPLACE(BOM_COMP_EQUIP_CHAMBER.DESIGN_TEMP_MAX_T, '&deg;', '°'), '\', '')
                            END AS DESIGNTEMP,
                            CASE
                                WHEN BOM_PLACE.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy' THEN BASE_FLUID.DESCRIPTION
                                ELSE (select BASE_FLUID.DESCRIPTION FROM BASE_FLUID Where ID = BOM_COMP_EQUIP_CHAMBER.FLUID)
                            END AS FLUID
                        FROM BOM_PLACE 
                        JOIN BOM_COMP ON BOM_COMP.PARENT_ID = BOM_PLACE.COMPONENT_ID 
                        LEFT JOIN BOM_PLACE_LINE ON BOM_PLACE_LINE.ID = BOM_PLACE.ID
                        LEFT JOIN BOM_COMP_STR ON BOM_COMP.ID = BOM_COMP_STR.ID
                        LEFT JOIN BOM_COMP_EQUIP_CHAMBER ON BOM_COMP_EQUIP_CHAMBER.ID = BOM_COMP_STR.ID
                        LEFT JOIN BASE_FLUID ON BASE_FLUID.ID = BOM_PLACE_LINE.FLUID
                        WHERE BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.EquipmentChamberComponentStrategy'
                        AND BOM_PLACE.ID = {assetIds}
            "

                );
            await connection.CloseAsync();
            return assetChambers.ToList();

        }

        public async Task<List<AssetManagementSites>> GetClientLocationDataAsync()
        {
            try
            {
                var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
                await connection.OpenAsync();
                var clientLocation = await connection.QueryAsync<AssetManagementSites>($@"
                        SELECT DISTINCT
                            client.ID AS CLIENTID,
                            client.NAME AS CLIENTNAME,
                            location.ID AS LOCATIONID,
                            location.NAME AS LOCATIONNAME,
                            costcenter.ID AS COSTCENTERID,
                            costcenter.DESCRIPTION AS COSTCENTERNAME
                            FROM
                                BOM_PLACE_ANCESTORS anc
                                LEFT JOIN BOM_PLACE client ON client.ID = anc.ANCESTOR
                                LEFT JOIN BOM_PLACE_STR client_str ON client_str.ID = client.ID
                                LEFT JOIN BASE_COMP_FAMILY client_family ON client_family.ID = client_str.COMPONENT_FAMILY
                                LEFT JOIN BOM_PLACE location ON location.ID = anc.DESCENDENT
                                LEFT JOIN BOM_PLACE_STR location_str ON location_str.ID = location.ID
                                LEFT JOIN BASE_COMP_FAMILY location_family ON location_family.ID = location_str.COMPONENT_FAMILY
                                LEFT JOIN BASE_PLACECOSTCENTER costcenter ON costcenter.ID = location_str.PLACECOSTCENTER
                            WHERE
                                client_family.DESCRIPTION = 'OIS CLIENT'
                                AND location_family.DESCRIPTION = 'OIS LOCATION'
                                AND costcenter.ID IS NOT NULL
                            ORDER BY
                                client.NAME,
                                location.NAME;"
                                    );
                await connection.CloseAsync();
                return clientLocation.ToList();
            }
            catch (Exception e)
            {
                throw new Exception($"Error while fetching client location data: {e.Message}", e);
            }
        }
        public async Task<List<AssetManagementSites>> GetClientLocationDataByIDAsync(ICollection<string>? districtid, ICollection<string>? clientid, ICollection<string>? role)
        {
            try
            {
                var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
                await connection.OpenAsync();
                var clientLocation = new List<AssetManagementSites>();
                var conditions = new List<string>();
                var parameters = new DynamicParameters();
                bool isAimaasAll = role.Any(r => r.Equals("aimaas:all", StringComparison.OrdinalIgnoreCase));
                bool hasDistricts = districtid != null && districtid.Any();
                bool hasClients = clientid != null && clientid.Any();

                if (hasDistricts)
                {
                    var districtConditions = districtid.Select((id, index) =>
                    {
                        var paramName = $"DistrictId{index}";
                        parameters.Add(paramName, $"{id}%");
                        return $"costcenter.DESCRIPTION LIKE @{paramName}";
                    });

                    conditions.Add($"({string.Join(" OR ", districtConditions)})");
                }

                if (hasClients)
                {
                    var clientConditions = clientid.Select((id, index) =>
                    {
                        var paramName = $"ClientId{index}";
                        parameters.Add(paramName, $"{id}");
                        return $"client.NAME = @{paramName}";
                    });

                    conditions.Add($"({string.Join(" OR ", clientConditions)})");
                }
                string joinOperator = (isAimaasAll && (hasDistricts ^ hasClients)) ? "OR" : "AND";
                //include 'AND' if both district and client filters exist
                var whereClause = conditions.Any() ? $"{joinOperator} (" + string.Join(" OR ", conditions) + ")" : "";

                clientLocation = (List<AssetManagementSites>)await connection.QueryAsync<AssetManagementSites>($@"
                SELECT DISTINCT
                    client.ID AS CLIENTID,
                    client.NAME AS CLIENTNAME,
                    location.ID AS LOCATIONID,
                    location.NAME AS LOCATIONNAME,
                    costcenter.ID AS COSTCENTERID,
                    costcenter.DESCRIPTION AS COSTCENTERNAME
                    FROM BOM_PLACE_ANCESTORS anc
                LEFT JOIN BOM_PLACE client ON client.ID = anc.ANCESTOR
                LEFT JOIN BOM_PLACE_STR client_str ON client_str.ID = client.ID
                LEFT JOIN BASE_COMP_FAMILY client_family ON client_family.ID = client_str.COMPONENT_FAMILY
                LEFT JOIN BOM_PLACE location ON location.ID = anc.DESCENDENT
                LEFT JOIN BOM_PLACE_STR location_str ON location_str.ID = location.ID
                LEFT JOIN BASE_COMP_FAMILY location_family ON location_family.ID = location_str.COMPONENT_FAMILY
                LEFT JOIN BASE_PLACECOSTCENTER costcenter ON costcenter.ID = location_str.PLACECOSTCENTER
                WHERE client_family.DESCRIPTION = 'OIS CLIENT'
                AND location_family.DESCRIPTION = 'OIS LOCATION'
                AND costcenter.ID IS NOT NULL
                {whereClause}
                ORDER BY client.NAME, location.NAME;", parameters);

                await connection.CloseAsync();
                return clientLocation.ToList();
            }
            catch(Exception e)
            {
                throw new Exception($"Error while fetching client location data: {e.Message} ",e);
            }
        }
        public async Task<List<GeneralAnalysis>> GetGeneralAnalysis(string assetId)
        {

            var generalAnalyses = new List<GeneralAnalysis>();
            var tempGeneralAnalysis = await GetGeneralAnalysisDetails(assetId);
            generalAnalyses.AddRange(tempGeneralAnalysis);
            return generalAnalyses;
        }
        public async Task<List<EquipmentData>> GetEquipmentData()

        {
            List<EquipmentData> equipData = new List<EquipmentData>();
            foreach (var assetManagementSite in _assetManagementSites1)
            {
                var _assetId_Strategies = await GetEquipmentIds(assetManagementSite.LOCATIONID);
                string ids = string.Join(", ", _assetId_Strategies.Select(s => $"'{s.ID}'"));

                EquipmentData equipmentData = new EquipmentData
                {
                    ClientID = assetManagementSite.CLIENTID,
                    ClientName = assetManagementSite.CLIENTNAME,
                    LocationID = assetManagementSite.LOCATIONID,
                    LocationName = assetManagementSite.LOCATIONNAME,
                    EquipmentList = _assetId_Strategies
                };
                equipData.Add(equipmentData);
            }
            return equipData;
        }

        private async Task<List<AnteaAsset>> GetEquipmentList(string locationId)
        {
            List<AnteaAsset> equipmentList = new List<AnteaAsset>();

            using (SqlConnection connection = new SqlConnection(_dataOptions.Value.ConnectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand command = new SqlCommand())
                {
                    command.Connection = connection;

                    command.CommandText = @"
            CREATE TABLE #BOM_Hierarchy_Temp (
                ID BIGINT,
                STRATEGYCLASSNAME VARCHAR(255)
            );

            WITH BOM_Hierarchy AS (
                -- Anchor member: Start with the given location ID
                SELECT
                    ID,
                    NAME,
                    STRATEGYCLASSNAME,
                    PARENT_ID,
                    COMPONENT_ID
                FROM BOM_PLACE
                WHERE ID = @locationId

                UNION ALL

                -- Recursive member: Get all children of the current node
                SELECT
                    b.ID,
                    b.NAME,
                    b.STRATEGYCLASSNAME,
                    b.PARENT_ID,
                    b.COMPONENT_ID
                FROM BOM_PLACE b
                INNER JOIN BOM_Hierarchy bh ON bh.ID = b.PARENT_ID
            )

            INSERT INTO #BOM_Hierarchy_Temp (ID, STRATEGYCLASSNAME)
            SELECT
                ID, STRATEGYCLASSNAME
            FROM BOM_Hierarchy
            WHERE STRATEGYCLASSNAME NOT IN (
                'it.imc.persistence.po.plants.place.GroupPlaceStrategy',
                'it.imc.persistence.po.bom.PUnitPlaceStrategy'
            );

            SELECT
                ID, ASSETID, AREAID, AREANAME, DESCRIPTION, SERVICE, EQUIPCATEGORY, ASSETTYPE,
                ASSETCATEGORY, ORIENTATION, CONSTRUCTIONCODE, INSPECTIONCODE, MANUFACTURER, CONSTRUCTIONYEAR,
                DIMENSIONS, SERIALNUMBER, NATIONALBOARD, LOCALJURIDICTIONAL, PID, CLIENTID, CLIENTNAME,
                LOCATIONID, LOCATIONNAME, CHAMBERS
            FROM ANTEAASSET
            WHERE ID IN (SELECT ID FROM #BOM_Hierarchy_Temp);

            DROP TABLE #BOM_Hierarchy_Temp;
            ";

                    command.Parameters.AddWithValue("@locationId", locationId);

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            equipmentList.Add(new AnteaAsset
                            {
                                ID = reader.GetString(reader.GetOrdinal("ID")),
                                ASSETID = reader.GetString(reader.GetOrdinal("ASSETID")),
                                AREAID = reader.GetString(reader.GetOrdinal("AREAID")),
                                AREANAME = reader.GetString(reader.GetOrdinal("AREANAME")),
                                DESCRIPTION = reader.GetString(reader.GetOrdinal("DESCRIPTION")),
                                SERVICE = reader.GetString(reader.GetOrdinal("SERVICE")),
                                EQUIPCATEGORY = reader.GetString(reader.GetOrdinal("EQUIPCATEGORY")),
                                ASSETTYPE = reader.GetString(reader.GetOrdinal("ASSETTYPE")),
                                ASSETCATEGORY = reader.GetString(reader.GetOrdinal("ASSETCATEGORY")),
                                ORIENTATION = reader.GetString(reader.GetOrdinal("ORIENTATION")),
                                CONSTRUCTIONCODE = reader.GetString(reader.GetOrdinal("CONSTRUCTIONCODE")),
                                INSPECTIONCODE = reader.GetString(reader.GetOrdinal("INSPECTIONCODE")),
                                MANUFACTURER = reader.GetString(reader.GetOrdinal("MANUFACTURER")),
                                CONSTRUCTIONYEAR = reader.GetInt32(reader.GetOrdinal("CONSTRUCTIONYEAR")),
                                DIMENSIONS = reader.GetString(reader.GetOrdinal("DIMENSIONS")),
                                SERIALNUMBER = reader.GetString(reader.GetOrdinal("SERIALNUMBER")),
                                NATIONALBOARD = reader.GetString(reader.GetOrdinal("NATIONALBOARD")),
                                LOCALJURIDICTIONAL = reader.GetString(reader.GetOrdinal("LOCALJURIDICTIONAL")),
                                PID = reader.GetString(reader.GetOrdinal("PID")),
                                CLIENTID = reader.GetString(reader.GetOrdinal("CLIENTID")),
                                CLIENTNAME = reader.GetString(reader.GetOrdinal("CLIENTNAME")),
                                LOCATIONID = reader.GetString(reader.GetOrdinal("LOCATIONID")),
                                LOCATIONNAME = reader.GetString(reader.GetOrdinal("LOCATIONNAME")),
                                CHAMBERS = reader.GetString(reader.GetOrdinal("CHAMBERS"))
                            });
                        }
                    }
                }

                return equipmentList;
            }
        }

        public async Task<List<Anomalies>> GetAnomaliesAsync()
        {
            await InitializeAsync();

            List<Anomalies> anomalies = new List<Anomalies>();
            string ids = string.Join(", ", _equipmentDataList
                 .SelectMany(ed => ed.EquipmentList)
                 .Select(s => $"'{s.ID}'"));
            var _anomalies = new List<Anomalies>();
            var tempAnomalies = await GetAnomalies(ids);
            _anomalies = tempAnomalies.GroupBy(o => o.ANOMALY).Select(g => g.First()).ToList();
            var mappedAnomaly = (from anomaly in _anomalies
                                 from equipmentData in _equipmentDataList
                                 from equipment in equipmentData.EquipmentList
                                 where anomaly.ASSETID == equipment.ID
                                 select new Anomalies
                                 {
                                     CLIENTID = equipmentData.ClientID,
                                     CLIENTNAME = equipmentData.ClientName,
                                     LOCATIONID = equipmentData.LocationID,
                                     LOCATIONNAME = equipmentData.LocationName,
                                     ASSETID = equipment.ID,
                                     PROPOSEDRECOMMEMENDATION = anomaly.PROPOSEDRECOMMEMENDATION,
                                     OPERATIONDATE = anomaly.OPERATIONDATE,
                                     DETECTIONDATE = anomaly.DETECTIONDATE,
                                     INSPECTIONOPERATIONINSTANCE = anomaly.INSPECTIONOPERATIONINSTANCE,
                                     ANOMALY = anomaly.ANOMALY,
                                     RESOLUTIONSTATE = anomaly.RESOLUTIONSTATE,
                                     ANOMALYDESCRIPTION = anomaly.ANOMALYDESCRIPTION,
                                     ANOMALYTYPE = anomaly.ANOMALYTYPE,
                                     ANOMALYPRIORITY = anomaly.ANOMALYPRIORITY,
                                     ANOMALYID = anomaly.ANOMALYID,
                                     OPERATIONID = anomaly.OPERATIONID,
                                     ASSETNAME = anomaly.ASSETNAME

                                 }).ToList();
            return mappedAnomaly;

        }
        public async Task<List<Anomalies>> GetAnomalies(string assetIds)
        {

            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);

            await connection.OpenAsync();

            var anomalies = await connection.QueryAsync<Anomalies>($@"
                select 
                distinct
                BASE_PRIORITY.DESCRIPTION AS ANOMALYPRIORITY,
                BASE_ANOMALY_TYPE.DESCRIPTION AS ANOMALYTYPE,
                PLAN_FINDING.DESCR AS ANOMALYDESCRIPTION,
                PLAN_FINDING.OPERATION AS OPERATIONID,
                BASE_FINDING_APP_STATE.DESCRIPTION AS RESOLUTIONSTATE,
                REPLACE(PLAN_FINDING.PROTOCOL,'Anom','A') AS ANOMALY,
                FORMAT(PLAN_FINDING.DETECTION_CAL,'MM/dd/yyyy')  AS DETECTIONDATE,
                FORMAT(PLAN_OPERATION.OPERATION_DATE, 'MM/dd/yyyy') AS OPERATIONDATE,
                CONCAT(BASE_PLANTITLE.DESCRIPTION,' ',FORMAT(PLAN_OPERATION.OPERATION_DATE, 'MM/dd/yyyy'),' ',BOM_PLACE.NAME) AS INSPECTIONOPERATIONINSTANCE,
                PLAN_FINDING.AC_DESC AS PROPOSEDRECOMMEMENDATION,
                PLAN_FINDING.ID AS ANOMALYID,
                BOM_PLACE.ID AS ASSETID,
                BOM_PLACE.Name AS ASSETNAME
                from PLAN_FINDING
                left join BASE_PRIORITY on BASE_PRIORITY.ID = PLAN_FINDING.PRIORITY
                left join BASE_ANOMALY_TYPE on BASE_ANOMALY_TYPE.ID = PLAN_FINDING.ANOMALYTYPE
                left join PLAN_OPERATION on PLAN_OPERATION.ID = PLAN_FINDING.OPERATION
                left join BASE_FINDING_APP_STATE on BASE_FINDING_APP_STATE.ID = PLAN_FINDING.FINDING_STATE
                join BASE_PLANTITLE_BASE_OP_TYPE on BASE_PLANTITLE_BASE_OP_TYPE.OPERATIONTYPES_ID = PLAN_OPERATION.OPERATIONTYPE_ID
                join BASE_PLANTITLE on BASE_PLANTITLE.ID = BASE_PLANTITLE_BASE_OP_TYPE.BASE_PLANTITLE_ID
                Left join BOM_PLACE on BOM_PLACE.ID = PLAN_OPERATION.PLACE_ID
                WHERE 
                    BOM_PLACE.ID IN ({assetIds});
                ");

            return anomalies.ToList();


        }
        public async Task<List<CorrosionAnalysis>> GetCorrosionAnalysisAsync(string operationId)
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);

            await connection.OpenAsync();
            var corrosionAnalysis = await connection.QueryAsync<CorrosionAnalysis>($@"
                
                         SELECT  

                    pt.NAME AS 'NAME' 

                    ,ptpp.DESCRIPTION AS 'POSITION' 

                    ,ptti.NOMINAL_THICKNESS_T AS 'NOMINALTHICKNESS' 

                    ,ptti.THICKNESS_LIMIT_T AS 'RETIREMENTTHICKNESS' 

                    ,FORMAT(po.OPERATION_DATE, 'MM-dd-yyyy') AS 'READINGDATE' 

                    ,ptm.THICKNESS_T AS 'READING' 

                    FROM BOM_PLACE bp 

                    LEFT JOIN PLAN_TML pt ON pt.PLACE_ID = bp.ID 

                    LEFT JOIN PLAN_TMLPOINT ptp ON ptp.OWNER_ID = pt.ID 

                    LEFT JOIN PLAN_TMLPOINTPOS ptpp ON ptpp.ID = ptp.POSITION_ 

                    LEFT JOIN PLAN_THICKNESSTMLINFO ptti ON ptti.TMLID = pt.ID 

                    LEFT JOIN PLAN_THICKNESS_MEASURE ptm ON ptm.POINT = ptp.ID 

                    LEFT JOIN PLAN_OPERATION po ON po.ID = ptm.THICKNESS_OP 

                    WHERE  

                    NOT pt.ID is NULL 

                    AND ptti.AVALUE LIKE 'MIN' 

                    -- Example of using operation ID for pulling a specific operation's thickness readings 

                    AND ptm.THICKNESS_OP = {operationId} 

                    ORDER BY bp.NAME, ptp.NAME;
                ");

            return corrosionAnalysis.ToList();

        }
        private async Task<List<Inspections>> GetInspections(string assetIds)
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);

            await connection.OpenAsync();
            var inspections = await connection.QueryAsync<Inspections>($@"
                                    SELECT
                                              utab.ASSETID    AS 'ASSETID',
                                                         utab.DESCRIPTION AS 'ASSETDESCRIPTION',
                                                         bbl.DESCRIPTION AS'ASSETSTATUS',
                                              Iif(
                                              (
                                                        SELECT    lnbp.NAME
                                                        FROM      BOM_PLACE_ANCESTORS anc
                                                        LEFT JOIN BOM_PLACE lnbp
                                                        ON        lnbp.ID = anc.ANCESTOR
                                                        WHERE     anc.DESCENDENT = parentp.ID
                                                        AND       STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LineNumberPlaceStrategy') IS NULL
                                     AND      parentp.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy',
                                              -- Asset is not Piping or does not have an ancestor Line Number
                                              parentp.NAME,
                                              -- Asset is Piping and has ancestor Line Number
                                              Concat_ws(': ',
                                              (
                                                        SELECT    lnbp.NAME
                                                        FROM      BOM_PLACE_ANCESTORS anc
                                                        LEFT JOIN BOM_PLACE lnbp
                                                        ON        lnbp.ID = anc.ANCESTOR
                                                        WHERE     anc.DESCENDENT = parentp.ID
                                                        AND       STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LineNumberPlaceStrategy'), parentp.NAME) ) AS 'ASSETIDNAME', -- DEFAULT COLUMN
                                              (
                                                        SELECT    lnbp.NAME
                                                        FROM      BOM_PLACE_ANCESTORS anc
                                                        LEFT JOIN BOM_PLACE lnbp
                                                        ON        lnbp.ID = anc.ANCESTOR
                                                        WHERE     anc.DESCENDENT = parentp.ID
                                                        AND       STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LineNumberPlaceStrategy' ) AS 'LINENUMBER',
                                              RefUnionCase                                                                                           AS 'OPERATIONORSCHEDULE', -- TESTING/REVIEW column only
                                              CASE
                                                       WHEN utab.STRATEGYCLASSNAME IN ('it.imc.persistence.po.plants.place.LineNumberPlaceStrategy',
                                                                                       'it.imc.persistence.po.plants.place.LinePlaceStrategy') THEN 'Piping'
                                                       WHEN utab.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.TankPlaceStrategy' THEN 'Tanks'
                                                       ELSE 'Vessels'
                                              END AS 'INSPECTIONASSETCATEGORY',
                                              (
                                                        SELECT    abp.NAME
                                                        FROM      BOM_PLACE_ANCESTORS anc
                                                        LEFT JOIN BOM_PLACE abp
                                                        ON        abp.ID = anc.ANCESTOR
                                                        WHERE     anc.DESCENDENT = parentp.ID
                                                        AND       STRATEGYCLASSNAME = 'it.imc.persistence.po.bom.PUnitPlaceStrategy' ) AS 'AREA', -- DEFAULT COLUMN
                                              ---- For the Asset name/ID, please include the Line Number concatenation behavior for Piping assets.
                                              --,'(not mapped for this example)' AS 'Asset Category' -- USER-OPTIONAL COLUMN (Please continue to use the same mapping as previously requested for this column/Equipment Category.)
                                              Iif(LEFT(Reverse(kvd.LISTOFS_VAL), 1) = ',', LEFT(kvd.LISTOFS_VAL, Len(kvd.LISTOFS_VAL) - 1), kvd.LISTOFS_VAL) AS 'RISKCLASS' -- USER-OPTIONAL COLUMN -- NOTE: Please ensure trailing commas are not brought in.
                                              ,
                                              bot.DESCRIPTION AS 'INSPECTIONTYPE' -- DEFAULT COLUMN
                                              ,
                                            FORMAT(utab.DUECALENDAR, 'MM-dd-yyyy')   AS 'COMPLETEDINSPECTIONDUE' -- DEFAULT COLUMN -- Note: Do not include 00:00:00.0000[...] timestamp in displayed result
                                              ,
                                              FORMAT(utab.OPERATION_DATE, 'MM-dd-yyyy')  AS 'INSPECTIONDATE' -- DEFAULT COLUMN -- Note: Do not include 00:00:00.0000[...] timestamp in displayed result
                                              ,
                                              bor.DESCRIPTION AS 'RESULT' -- DEFAULT COLUMN
                                              ,
                                              bos.DESCRIPTION AS 'INSPECTIONSTATE' -- USER-OPTIONAL COLUMN
                                              ,
                                             FORMAT(utab.NEXTCALENDAR, 'MM-dd-yyyy')  AS 'NEXTINSPECTIONDUE' -- DEFAULT COLUMN -- Note: Do not include 00:00:00.0000[...] timestamp in displayed result
                                              ,
                                              bpt.DESCRIPTION AS 'SCHEDULETYPE' -- DEFAULT COLUMN
                                              ,
                                              Iif(utab.RECURRENCETYPE LIKE 'NONE', NULL, Concat_ws(' ', utab.RECURRENCEFREQUENCY, utab.RECURRENCETYPE)) AS 'FREQUENCY' -- USER-OPTIONAL COLUMN
                                              ,
                                              utab.[ScheduleStatus] AS 'SCHEDULESTATUS'-- DEFAULT COLUMN
                                              ,
                                              utab.[INSPECTIONSTATUS] AS 'INSPECTIONSTATUS',
                                              utab.NOTES AS 'NOTES',
                                              utab.PERFORMER AS 'PERFORMER',
                                              (
                                                     SELECT msc.DESCRIPTION
                                                     FROM   BOM_MNGMNTSYSTEMCATEGORY_PS msp
                                                     JOIN   BASE_MANAGEMENT_SYSTEM_CAT msc
                                                     ON     msp.MNGMNT_SYS_CATEGORY_ID = msc.ID
                                                     WHERE  msp.PLACESTRATEGY_ID = utab.ASSETID FOR json auto ) AS 'ASSETMANAGEMENTCATEGORY'-- DEFAULT COLUMN,
                                              ,
                                              utab.SCHEDULEID  AS 'SCHEDULEID' ,
                                              utab.OPERATIONID AS 'PLANOPERATIONID',
                                              utab.NEXTDUEDATENOTES AS 'NEXTDUEDATENOTES'
		 
		
                                     FROM     (
                                              (
                                                        SELECT -- Operations that are related to schedules explicitly
                                                                  bp.STRATEGYCLASSNAME,
                                                                  bp.ID AS 'ASSETID' ,
                                                                  bp.NAME ,
                                                                  bp.DESCRIPTION,
                                                                  po.OPERATIONTYPE_ID ,
                            
                                                                  po.OPERATION_DATE ,
                                                                  po.OPERATIONRESULT_ID ,
                                                                  po.OPSTATE_ID ,
                                                                  pp.NEXTCALENDAR ,
                                                                  pp.PLANNINGTITLE_ID ,
                                                                  pp.RECURRENCEFREQUENCY ,
                                                                  pp.RECURRENCETYPE ,
                                                                  CASE
                                                                            WHEN po.OPSTATE_ID = 623579470028607488 THEN 'Active' -- 'Scheduled' op_state ID
                                                                            WHEN pp.LASTCLOSED_ID IN
                                                                                      (
                                                                                             SELECT spd.ID
                                                                                             FROM   PLAN_DATE spd
                                                                                             WHERE  spd.OPERATION_ID = po.ID) THEN 'Active' -- (by ID)
                                                                            WHEN pp.LASTPERFCALENDAR = po.OPERATION_DATE THEN 'Active'      -- (by date w/ PLAN_DATE relationship via schedule)
                                                                            WHEN OperationCount = 1 THEN 'Active'                           -- (by being the sole operation of this type)
                                                                            WHEN po.OPERATION_DATE = MaxOperationDate THEN 'Active'         -- (by date = max date of insp type)
                                                                            ELSE 'Historical'
                                                                  END AS 'ScheduleStatus' ,
                                                                  CASE
                                                                            WHEN po.OPSTATE_ID = 623579470028607488 THEN 'Future Inspection' -- 'Scheduled' op_state ID
                                                                            ELSE 'Past Inspection'
                                                                  END AS 'INSPECTIONSTATUS' ,
                                                                  pp.DISABLED ,
                                                                  'Explicit Operation-Schedule' AS 'RefUnionCase' -- TESTING/REVIEW column only
                                                                  ,
                                                                  pp.ID AS 'SCHEDULEID' ,
                                                                  po.ID AS 'OPERATIONID',
                                                                  po.NOTES_ AS 'NOTES',
                                                                  PB_ITEM.SUBJECT_NAME AS 'PERFORMER',
                                                                 IIF(pp.LASTCLOSED_ID IS NULL, NULL, cpd.DUECALENDAR) AS 'DUECALENDAR',
                                                                 textpd.TEXT AS 'NEXTDUEDATENOTES'
                                                        FROM      BOM_PLACE bp
                                                        LEFT JOIN PLAN_PLANNING pp
                                                        ON        bp.ID = pp.PLACE_ID
					                                    LEFT JOIN PLAN_DATE textpd ON textpd.PLANNING_ID = pp.ID AND textpd.TEXT IS NOT NULL AND textpd.COMPLETION_STATUS LIKE 'OPEN'
                                                        LEFT JOIN PLAN_DATE cpd
                                                        ON        cpd.PLANNING_ID = pp.ID
                                                        AND       cpd.COMPLETION_STATUS LIKE 'CLOSED'
                                                        LEFT JOIN PLAN_OPERATION po
                                                        ON        po.ID = cpd.OPERATION_ID
                                                        LEFT JOIN
                                                                  (
                                                                           SELECT   po.PLACE_ID ,
                                                                                    max(po.OPERATION_DATE)   AS 'MaxOperationDate' ,
                                                                                    count(po.OPERATION_DATE) AS 'OperationCount' ,
                                                                                    po.OPERATIONTYPE_ID
                                                                           FROM     PLAN_OPERATION po
                                                                           GROUP BY PLACE_ID,
                                                                                    OPERATIONTYPE_ID ) maxop
                                                        ON        maxop.PLACE_ID = bp.ID
                                                        AND       maxop.OPERATIONTYPE_ID = po.OPERATIONTYPE_ID
                                                        LEFT JOIN PB_ITEM on PB_ITEM.ID = po.PERFORMER_ID
                                                        WHERE     pp.ID IS NOT NULL
                                                        AND       po.ID IS NOT NULL )
                                     UNION
                                           (
                                                     SELECT -- Operations that are not explicitly related to schedules
                                                               bp.STRATEGYCLASSNAME,
                                                               bp.ID AS 'ASSETID' ,
                                                               bp.NAME ,
                                                               bp.DESCRIPTION,
                                                               po.OPERATIONTYPE_ID ,
                          
                                                               po.OPERATION_DATE ,
                                                               po.OPERATIONRESULT_ID ,
                                                               po.OPSTATE_ID ,
                                                               NULL AS 'NEXTCALENDAR' ,
                                                               NULL AS 'PLAN_DESCRIPTION' ,
                                                               NULL AS 'RECURRENCEFREQUENCY' ,
                                                               NULL AS 'RECURRENCETYPE' ,
                                                               CASE
                                                                         WHEN po.OPSTATE_ID = 623579470028607488 THEN 'Due Date Unknown' --Op State ID = Scheduled
                                                                         ELSE 'No Schedule'
                                                               END AS 'SCHEDULESTATUS' ,
                                                               CASE
                                                                         WHEN po.OPSTATE_ID = 623579470028607488 THEN 'Future Inspection' --Op State ID = Scheduled
                                                                         ELSE 'Past Inspection'
                                                               END AS 'INSPECTIONSTATUS' ,
                                                               ''  AS 'DISABLED' -- Empty string instead of NULL prevents the current main SELECT's WHERE clause from excluding these rows
                                                               ,
                                                               'Operation, No Schedule' AS 'RefUnionCase' -- TESTING/REVIEW column only,
                                                               ,
                                                               NULL  AS 'SCHEDULEID' ,
                                                               po.ID AS 'OPERATIONID',
                                                               po.NOTES_ AS 'NOTES',
                                                                PB_ITEM.SUBJECT_NAME AS 'PERFORMER',
                                                                NULL AS 'DUECALENDAR' ,
                                                                NULL AS 'NEXTDUEDATENOTES'
                                                     FROM      BOM_PLACE bp
                                                     LEFT JOIN PLAN_OPERATION po
                                                     ON        po.PLACE_ID = bp.ID
                                                     LEFT JOIN PB_ITEM on PB_ITEM.ID = po.PERFORMER_ID
                                                     WHERE     (
                                                                      SELECT count(cpd.ID)
                                                                      FROM   PLAN_DATE cpd
                                                                      WHERE  cpd.OPERATION_ID = po.ID) = 0
                                                     AND       po.ID IS NOT NULL )
                                     UNION
                                               (
                                                         SELECT -- Schedules that have no operations
                                                                   bp.STRATEGYCLASSNAME,
                                                                   bp.ID AS 'ASSETID' ,
                                                                   bp.NAME ,
                                                                   bp.DESCRIPTION,
                                                                   NULL            AS 'OPERATIONTYPE_ID' ,
                                                                 --  cpd.DUECALENDAR AS 'DUECALENDAR' ,
                                                                   NULL            AS 'OPERATION_DATE' ,
                                                                   NULL            AS 'OPERATIONRESULT_ID' ,
                                                                   NULL            AS 'OPSTATE_ID' ,
                                                                   pp.NEXTCALENDAR ,
                                                                   pp.PLANNINGTITLE_ID ,
                                                                   pp.RECURRENCEFREQUENCY ,
                                                                   pp.RECURRENCETYPE ,
                                                                   'Active'       AS 'SCHEDULESTATUS' ,
                                                                   'No Inspection' AS 'INSPECTIONSTATUS' ,
                                                                   pp.DISABLED ,
                                                                   'Schedule, No Operation' AS 'RefUnionCase'-- TESTING/REVIEW column only
                                                                   ,
                                                                   pp.ID AS 'SCHEDULEID' ,
                                                                   NULL  AS 'OPERATIONID',
                                                                   '' AS 'NOTES',
                                                                   '' AS 'PERFORMER',
                                                                   IIF(pp.LASTCLOSED_ID IS NULL, NULL, cpd.DUECALENDAR) AS 'DUECALENDAR',
                                                                   textpd.TEXT AS 'NEXTDUEDATENOTES'
                                                         FROM      BOM_PLACE bp
                                                         LEFT JOIN PLAN_PLANNING pp
                                                         ON        bp.ID = pp.PLACE_ID
					                                     LEFT JOIN PLAN_DATE textpd ON textpd.PLANNING_ID = pp.ID AND textpd.TEXT IS NOT NULL AND textpd.COMPLETION_STATUS LIKE 'OPEN'
                                                         LEFT JOIN PLAN_DATE cpd
                                                         ON        cpd.PLANNING_ID = pp.ID
                                                         AND       cpd.COMPLETION_STATUS LIKE 'OPEN'
                                                         WHERE     pp.ID IS NOT NULL
                                                         AND
                                                                   (
                                                                          SELECT count(spd.ID)
                                                                          FROM   PLAN_DATE spd
                                                                          WHERE  spd.PLANNING_ID = pp.ID
                                                                          AND    spd.COMPLETION_STATUS LIKE 'CLOSED') = 0
                                                         AND       (
                                                                             pp.NEXTCALENDAR = cpd.DUECALENDAR
                                                                   OR        pp.NEXTCALENDAR IS NULL) ) ) utab
                                     LEFT JOIN BASE_OP_TYPE bot
                                     ON        bot.ID = utab.OPERATIONTYPE_ID
                                     LEFT JOIN BASE_PLANTITLE bpt
                                     ON        bpt.ID = utab.PLANNINGTITLE_ID
                                      LEFT JOIN BOM_PLACE_STR bps ON bps.ID =utab.ASSETID
                                         LEFT JOIN BASE_BY_LAW bbl ON bbl.ID = bps.BYLAW_ID
                                     LEFT JOIN BASE_OP_RESULT bor
                                     ON        bor.ID = utab.OPERATIONRESULT_ID
                                     LEFT JOIN BASE_OP_STATE bos
                                     ON        bos.ID = utab.OPSTATE_ID
                                     LEFT JOIN BOM_PLACE parentp
                                     ON        parentp.ID = utab.ASSETID
                                     LEFT JOIN KV_DEFENTRY kvd
                                     ON        kvd.OBJ_ID = utab.ASSETID

                                     AND       kvd.PKV_TEMPLATE =
                                               (
                                                      SELECT bpkv.ID
                                                      FROM   BASE_PLACEKVTEMPLATE bpkv
                                                      WHERE  bpkv.CODE = 'Risk Class')
                                      WHERE    utab.DISABLED = 0
                                            AND      utab.ASSETID IN ( {assetIds})
                                      ORDER BY utab.NAME,
                                               utab.OPERATION_DATE ;
                        "
);
            await connection.CloseAsync();
            return inspections.ToList(); ;
        }
        private async Task<List<AssetAttachments>> GetAssetAttachments(string assetIds)
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);

            await connection.OpenAsync();
            var assetAttachments = await connection.QueryAsync<AssetAttachments>($@"
                 select 
                 dm.DOCUMENT_ID AS DOCUMENTID,
                 ds.OBJECTID as ASSETID,
                 dm.DESCRIPTION AS DESCRIPTION,
                 dm.DOCUMENTNAME as FILENAME,
                 coalesce(dm.CONTENTTYPE,dm.DOCUMENTNAME) AS FILETYPE,
                 bdg.DESCRIPTION AS DOCUMENTTYPE,
                 dm.CREATIONDATE AS CREATEDDATE,
                 dm.DOCUMENTPATH AS FILELINK
                 from DOCUMENTS_METADATA dm
                 join DOCUMENTS_ASSOCIATION ds on ds.DOCUMENTID = dm.DOCUMENT_ID
                 left join BASE_DOC_GROUP bdg on bdg.ID = dm.DOCUMENTSGROUP
                 where ds.OBJECTID IN ({assetIds})
");
            await connection.CloseAsync();
            return assetAttachments.ToList();

        }
        private async Task<List<AssetComponents>> GetAssetComponentDetails(string assetIds)
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);

            await connection.OpenAsync();
            var assetComponents = await connection.QueryAsync<AssetComponents>($@"
               CREATE TABLE #FinalResults(
	                ASSETID NVARCHAR(255),
	                COMPONENTID VARCHAR(255),
	                COMPONENTNAME NVARCHAR(255),
	                DESCRIPTION NVARCHAR(255),
	                MATERIAL NVARCHAR(255),
	                OUTSIDEDIAMETER NVARCHAR(255),
	                HEADTYPE NVARCHAR (255),
	                NOTES NVARCHAR(MAX),
	                JOINTEFFICIENCY NVARCHAR(255),
	                ALLOWABLESTRESS NVARCHAR(255),
	                NOMINALTHICKNESS NVARCHAR(255),
	                CORROSIONALLOWANCE NVARCHAR(255),
	                REASSESEDLIMITTHICKNESS NVARCHAR(255)	
	                );
	                select ID as ASSETID,COMPONENT_ID,BOM_PLACE.NAME AS ASSETNAME INTO #TempAssetComponentDetails from BOM_PLACE  where ID IN ({assetIds})
	                --declare cursor for assetid and componentid
	                DECLARE AssetComponentCursor CURSOR FOR 
	                SELECT ASSETID,COMPONENT_ID AS COMPONENTPARENTID,ASSETNAME 
	                FROM #TempAssetComponentDetails;
	                DECLARE @AssetId NVARCHAR(255), @ComponentParentId NVARCHAR(255),@AssetName NVARCHAR(255);

	                OPEN AssetComponentCursor;
	                FETCH NEXT FROM AssetComponentCursor INTO @AssetId,@ComponentParentId,@AssetName;

	                WHILE @@FETCH_STATUS =0
	                BEGIN
				                WITH BOM_Hierarchy AS (
					                -- Anchor member: start with the given component id
					                SELECT 
						                ID,
						                STRATEGYCLASSNAME,
						                PARENT_ID
					                FROM 
						                BOM_COMP
					                WHERE 
						                ID = @ComponentParentId
    
					                UNION ALL
    
					                -- Recursive member: get all children of the current node
					                SELECT 
						                b.ID,
						                b.STRATEGYCLASSNAME,
						                b.PARENT_ID
					                FROM 
						                BOM_COMP b
					                INNER JOIN 
						                BOM_Hierarchy bh ON bh.ID = b.PARENT_ID
				                )

				                -- Insert the filtered data into a temporary table
				                SELECT 
					                ID AS COMPONENTID, 
					                STRATEGYCLASSNAME,
					                @AssetId AS ASSETID,
					                @AssetName AS ASSETNAME
				                INTO 
					                #TempComponentsIDTable 
				                FROM 
					                BOM_Hierarchy 
				                WHERE 
					                STRATEGYCLASSNAME IN 
					                (
						                'it.imc.persistence.po.plants.component.ShellComponentStrategy',
						                'it.imc.persistence.po.plants.component.NozzleComponentStrategy',
						                'it.imc.persistence.po.plants.component.TankBottomComponentStrategy',
						                'it.imc.persistence.po.plants.component.TankFixedRoofComponentStrategy',
						                'it.imc.persistence.po.plants.component.TankShellComponentStrategy',
						                'it.imc.persistence.po.plants.component.BottomComponentStrategy',
						                'it.imc.persistence.po.plants.component.TubeComponentStrategy'
					                );
						
				                DECLARE ComponentCursor CURSOR FOR 
					                select COMPONENTID,ASSETID 
					                from #TempComponentsIDTable ;
				                DECLARE @ComponentId NVARCHAR(255),@ChildAssetId NVARCHAR(255);
				
				                OPEN ComponentCursor;
				                FETCH NEXT FROM ComponentCursor INTO @ComponentId,@ChildAssetId;

				                WHILE @@FETCH_STATUS = 0
				                BEGIN
				                INSERT INTO #FinalResults (
					
					                ASSETID,COMPONENTID,COMPONENTNAME,DESCRIPTION,MATERIAL,OUTSIDEDIAMETER,HEADTYPE,NOTES,JOINTEFFICIENCY,ALLOWABLESTRESS,NOMINALTHICKNESS,CORROSIONALLOWANCE,REASSESEDLIMITTHICKNESS
				                )
				                 SELECT 
						                 @ChildAssetId AS ASSETID,
						                 BOM_COMP.ID AS COMPONENTID, 
						                 BOM_COMP.NAME AS COMPONENTNAME,
						                 BOM_COMP.DESCRIPTION AS DESCRIPTION,
						                 BASE_MATERIAL.DESCRIPTION AS MATERIAL,
						                 CASE
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.ShellComponentStrategy' then REPLACE( PLANTS_COMP_SHELL.DIAM_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.NozzleComponentStrategy' then REPLACE( PLANTS_COMP_NOZZLE.DIAM_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankShellComponentStrategy' then REPLACE( PLANTS_COMP_TANKSHELL.DIAM_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankBottomComponentStrategy' then REPLACE( PLANTS_COMP_TNKBOTTOM.DIAM_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankFixedRoofComponentStrategy' then REPLACE( PLANTS_COMP_TNKFXDROOF.DIAM_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.BottomComponentStrategy' then REPLACE( PLANTS_COMP_BOTTOM.DIAM_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TubeComponentStrategy' then REPLACE( PLANTS_COMP_TUBE.DIAM_T,'\',' ')
							                 ELSE NULL
						                 END AS OUTSIDEDIAMETER,
                                             CASE 

                                              WHEN PLANTS_COMP_BOTTOM.BOTTOM_TYPE = 'CONVEX'			THEN 'Ellipsoidal' 

                                              WHEN PLANTS_COMP_BOTTOM.BOTTOM_TYPE = 'DOUBLE_BOTTOM'		THEN 'Double bottom' 

                                              WHEN PLANTS_COMP_BOTTOM.BOTTOM_TYPE = 'FITTING'		THEN 'Fitting' 

                                              WHEN PLANTS_COMP_BOTTOM.BOTTOM_TYPE = 'HEMISPHERIC'		THEN 'Hemispheric' 

                                              WHEN PLANTS_COMP_BOTTOM.BOTTOM_TYPE = 'MITRE'			THEN 'Mitre' 

                                              WHEN PLANTS_COMP_BOTTOM.BOTTOM_TYPE = 'PLANE'			THEN 'Flat' 

                                              WHEN PLANTS_COMP_BOTTOM.BOTTOM_TYPE = 'SINGLE_BOTTOM'		THEN 'Single bottom' 

                                              WHEN PLANTS_COMP_BOTTOM.BOTTOM_TYPE = 'TANG'			THEN 'Tang' 

                                              END AS 'Head Type' ,
						                 --CASE when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.BottomComponentStrategy' then PLANTS_COMP_BOTTOM.BOTTOM_TYPE
						                -- ELSE NULL
						                 --END AS HEADTYPE,
						                 BOM_COMP.NOTES_ AS NOTES,
						                 CASE
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.ShellComponentStrategy' then PLANTS_COMP_SHELL.JOINT_EFF
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankShellComponentStrategy' then BOM_PLACE_TANK.SHELL_WELDING_EFFICENCY
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankBottomComponentStrategy' then BOM_PLACE_TANK.BOTTOM_WELDING_EFFICENCY
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.BottomComponentStrategy' then PLANTS_COMP_BOTTOM.JOINT_EFF
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TubeComponentStrategy' then PLANTS_COMP_TUBE.JOINT_EFF
							                 ELSE NULL
						                 END AS JOINTEFFICIENCY,
						                 CASE
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.ShellComponentStrategy' then REPLACE( PLANTS_COMP_SHELL.ALL_STRESS_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.BottomComponentStrategy' then REPLACE( PLANTS_COMP_BOTTOM.ALL_STRESS_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TubeComponentStrategy' then REPLACE( PLANTS_COMP_TUBE.ALL_STRESS_T,'\',' ')
							                 ELSE NULL
						                 END AS ALLOWABLESTRESS,
						                 CASE
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.ShellComponentStrategy' then REPLACE( PLANTS_COMP_SHELL.THICKNESS_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.NozzleComponentStrategy' then REPLACE( PLANTS_COMP_NOZZLE.THICKNESS_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankShellComponentStrategy' then REPLACE( PLANTS_COMP_TANKSHELL.THICKNESS_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankBottomComponentStrategy' then REPLACE( PLANTS_COMP_TNKBOTTOM.THICKNESS_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankFixedRoofComponentStrategy' then REPLACE( PLANTS_COMP_TNKFXDROOF.THICKNESS_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.BottomComponentStrategy' then REPLACE( PLANTS_COMP_BOTTOM.THICKNESS_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TubeComponentStrategy' then REPLACE( PLANTS_COMP_TUBE.THICKNESS_T,'\',' ')
							                 ELSE NULL
						                 END AS NOMINALTHICKNESS,
						                 CASE
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.ShellComponentStrategy' then REPLACE( PLANTS_COMP_SHELL.COR_ALLOWANCE_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.NozzleComponentStrategy' then REPLACE( PLANTS_COMP_NOZZLE.COR_ALLOWANCE_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankShellComponentStrategy' then REPLACE( PLANTS_COMP_TANKSHELL.COR_ALLOWANCE_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankBottomComponentStrategy' then REPLACE( PLANTS_COMP_TNKBOTTOM.COR_ALLOWANCE_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TankFixedRoofComponentStrategy' then REPLACE( PLANTS_COMP_TNKFXDROOF.COR_ALLOWANCE_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.BottomComponentStrategy' then REPLACE( PLANTS_COMP_BOTTOM.COR_ALLOWANCE_T,'\',' ')
							                 when BOM_COMP.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.component.TubeComponentStrategy' then REPLACE( PLANTS_COMP_TUBE.COR_ALLOWANCE_T,'\',' ')
							                 ELSE NULL
						                 END AS CORROSIONALLOWANCE,
						                 REPLACE((select VALUE_T from FFS_FFSASSESSMENT where DTYPE='minimumThk' and REPRESENTATIVE = 1 and SUBCOMPONENTID = BOM_COMP.ID),'\',' ') AS REASSESEDLIMITTHICKNESS

						                 FROM BOM_COMP
						                 join BOM_COMP_STR bcs on BOM_COMP.ID = bcs.ID
						                 left join BASE_MATERIAL on BASE_MATERIAL.ID = bcs.MATERIAL
						                 left join PLANTS_COMP_NOZZLE on PLANTS_COMP_NOZZLE.ID = bcs.ID 
						                 left join PLANTS_COMP_SHELL on PLANTS_COMP_SHELL.ID = bcs.ID
						                 left join PLANTS_COMP_TANKSHELL on PLANTS_COMP_TANKSHELL.ID = bcs.ID
						                 left join PLANTS_COMP_TNKBOTTOM on PLANTS_COMP_TNKBOTTOM.ID = bcs.ID
						                 left join PLANTS_COMP_TNKFXDROOF on PLANTS_COMP_TNKFXDROOF.ID = bcs.ID
						                 left join PLANTS_COMP_BOTTOM on PLANTS_COMP_BOTTOM.ID = bcs.ID
						                 left join PLANTS_COMP_TUBE on PLANTS_COMP_TUBE.ID = bcs.ID
						                 left join BOM_PLACE_TANK on BOM_PLACE_TANK.ID = @ChildAssetId
						                 where BOM_COMP.ID = @ComponentId;


				                FETCH NEXT FROM ComponentCursor INTO @ComponentId,@ChildAssetId;
				                END;

				                CLOSE ComponentCursor;
				                DEALLOCATE ComponentCursor;
		
	
	                --Fetch next row
	                DROP TABLE #TempComponentsIDTable;
	                FETCH NEXT FROM AssetComponentCursor INTO @AssetId,@ComponentParentId,@AssetName;
	                END;
	                CLOSE AssetComponentCursor;
	                DEALLOCATE AssetComponentCursor;

	                --Select Results 
	                select * from #FinalResults;
	                DROP TABLE #FinalResults;
	                DROP TABLE #TempAssetComponentDetails;"

                );
            await connection.CloseAsync();
            return assetComponents.ToList();

        }

        private async Task<List<GeneralAnalysis>> GetGeneralAnalysisDetails(string assetIds)
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();
            var generalAnalysisDetails = await connection.QueryAsync<GeneralAnalysis>($@"
               
WITH ThickOps_CTE (PLACE_ID, TML, OPERATION, OPERATION_DATE, THICKNESS_T, INCLUDED_MEASURE)
AS
(
	SELECT
		po.PLACE_ID
		,ptot.TML
		,ptot.OPERATION
		,po.OPERATION_DATE
		,ptm.THICKNESS_T
		,ptm.INCLUDED_MEASURE
	FROM PLAN_THICKNESSOP_TML ptot 
		LEFT JOIN PLAN_TML pt ON pt.ID = ptot.TML
		LEFT JOIN PLAN_TMLPOINT ptp ON ptp.OWNER_ID = pt.ID
		LEFT JOIN PLAN_THICKNESS_MEASURE ptm ON ptm.THICKNESS_OP = ptot.OPERATION AND ptp.ID = ptm.POINT
		LEFT JOIN PLAN_OPERATION po ON ptot.OPERATION = po.ID
)
,EarliestED_CTE (PLACEID, EarliestED)
AS
(
	SELECT
		PLACEID
		,MIN(EARLIEST_EXPIRY_DATE) AS 'EarliestED'
	FROM PLAN_THICKNESSTMLINFO sptti
	WHERE sptti.AVALUE LIKE 'MIN'
		GROUP BY PLACEID
)
,HighestCR_CTE (PLACEID, HighestCR, EarliestED)
AS
(
	SELECT
		PLACEID
		,MAX(MAX_CORROSION_RATE) AS 'HighestCR'
		,EARLIEST_EXPIRY_DATE AS 'EarliestED'
	FROM PLAN_THICKNESSTMLINFO sptti
	WHERE sptti.AVALUE LIKE 'MIN'
		GROUP BY PLACEID, EARLIEST_EXPIRY_DATE
)
SELECT 
	---- If CMLs have identical CRs and EDs, then only select 1 for a given asset; it shouldn't matter which one.
	TOP 1 WITH TIES

	---- For testing reference and results' review
	pt.PLACE_ID AS 'ASSETID',
	
	---- For displaying asset hierarchy info
	------- EM: In my testing using a subquery instead of a JOIN or CTE is faster to gather this data. I'm not sure why.
	--(SELECT abp.NAME 
	--	FROM BOM_PLACE_ANCESTORS anc
	--		LEFT JOIN BOM_PLACE abp ON abp.ID = anc.ANCESTOR
	--		LEFT JOIN BOM_PLACE_STR bps ON bps.ID = abp.ID
	--		LEFT JOIN BASE_COMP_FAMILY bcf ON bcf.ID = bps.COMPONENT_FAMILY
	--	WHERE anc.DESCENDENT = bp.ID
	--		AND STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.GroupPlaceStrategy'
	--		AND bcf.DESCRIPTION = 'OIS CLIENT') AS 'CLIENTNAME', -- For reference during testing/review
	--(SELECT abp.NAME 
	--	FROM BOM_PLACE_ANCESTORS anc
	--		LEFT JOIN BOM_PLACE abp ON abp.ID = anc.ANCESTOR
	--		LEFT JOIN BOM_PLACE_STR bps ON bps.ID = abp.ID
	--		LEFT JOIN BASE_COMP_FAMILY bcf ON bcf.ID = bps.COMPONENT_FAMILY
	--	WHERE anc.DESCENDENT = bp.ID
	--		AND STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.GroupPlaceStrategy'
	--		AND bcf.DESCRIPTION = 'OIS LOCATION') AS 'LOCATIONNAME', -- Client/facility -- For reference during testing/review
	--	bp.NAME AS 'Asset', -- For reference during testing/review
	
	FORMAT(ptti.EARLIEST_EXPIRY_DATE,'MM/dd/yyyy' )AS 'RETIREMENTDATE'

	-- NOTE: Round and add unit: years. If RemainingLife is negative/expiration date is in the past, display ""PAST RETIREMENT"" instead.
	,DATEDIFF(day, GETDATE(), ptti.EARLIEST_EXPIRY_DATE)/365.25 AS 'RemainingLife' 

	,(SELECT NAME FROM BOM_COMP sbc WHERE sbc.ID = pt.COMPONENT_ID) AS 'DRIVINGCOMPONENT'
	,pt.NAME AS 'DRIVINGCML'
	
	,IIF(ptti.CORROSION_RATE_T LIKE '%mm%', -- NOTE: Convert to inches if in millimeters
		CAST(ptti.ST_CORROSION_RATE/25.4 AS NVARCHAR(MAX)),
		ptti.CORROSION_RATE_T) AS 'STCorrosionRate' -- NOTE: Remove \, preserve unit of in, round to correct number of decimal places, ensure not in scientific notation
	,IIF(ptti.CORROSION_RATE_LT_T LIKE '%mm%',   -- NOTE: Convert to inches if in millimeters
		CAST(ptti.LT_CORROSION_RATE/25.4 AS NVARCHAR(MAX)),
		ptti.CORROSION_RATE_LT_T) AS 'LTCorrosionRate' -- NOTE: Remove \, preserve unit of in, round to correct number of decimal places, ensure not in scientific notation
	
	,ptti.NOMINAL_THICKNESS_T AS 'NOMINALTHICKNESS'
	,ptti.THICKNESS_LIMIT_T AS 'RETIREMENTTHICKNESS'
	,FORMAT(LastDate.Date,'MM/dd/yyyy' )AS 'LASTDATE'
	,(SELECT MIN(toc.THICKNESS_T) FROM ThickOps_CTE toc WHERE toc.OPERATION_DATE = LastDate.Date AND toc.TML = pt.ID) AS 'LASTVALUE'
	,FORMAT(PrevDate.Date,'MM/dd/yyyy' )AS 'PREVIOUSDATE'
	,(SELECT MIN(toc.THICKNESS_T) FROM ThickOps_CTE toc WHERE toc.OPERATION_DATE = PrevDate.Date AND toc.TML = pt.ID) AS 'PREVIOUSVALUE'
	,FORMAT(FirstDate.Date,'MM/dd/yyyy' )AS 'FIRSTDATE'
	,(SELECT MIN(toc.THICKNESS_T) FROM ThickOps_CTE toc WHERE toc.OPERATION_DATE = FirstDate.Date AND toc.TML = pt.ID) AS 'FIRSTVALUE'
FROM PLAN_TML pt
	LEFT JOIN BOM_PLACE bp ON bp.ID = pt.PLACE_ID
	LEFT JOIN PLAN_THICKNESSTMLINFO ptti ON ptti.TMLID = pt.ID

	---- JOINs for Driving CML logic
	LEFT JOIN EarliestED_CTE WorstExpiryDate ON WorstExpiryDate.PLACEID = bp.ID
	LEFT JOIN HighestCR_CTE HighestCR ON HighestCR.PLACEID = bp.ID
	LEFT JOIN
	(
		SELECT
			subtmli.PLACEID
			,COUNT(*) AS 'DriverCount'
		FROM PLAN_THICKNESSTMLINFO subtmli 
			LEFT JOIN EarliestED_CTE ee ON ee.PLACEID = subtmli.PLACEID
			LEFT JOIN HighestCR_CTE hc ON hc.PLACEID = subtmli.PLACEID
		WHERE AVALUE LIKE 'MIN'
			AND ee.EarliestED = hc.EarliestED 
			AND ee.EarliestED = subtmli.EARLIEST_EXPIRY_DATE 
			AND (hc.HighestCR = subtmli.MAX_CORROSION_RATE OR subtmli.MAX_CORROSION_RATE IS NULL)
		GROUP BY subtmli.PLACEID
	) DriverCounts ON DriverCounts.PLACEID = bp.ID

	---- JOINs for thickness reading history
	LEFT JOIN (
		SELECT 
			toc.TML
			,MAX(OPERATION_DATE) AS 'Date' 
		FROM ThickOps_CTE toc 
		WHERE toc.INCLUDED_MEASURE = 1 
		GROUP BY toc.TML
	) LastDate ON LastDate.TML = pt.ID
	LEFT JOIN (
		SELECT 
			toc.TML
			,MAX(OPERATION_DATE) AS 'Date' 
		FROM ThickOps_CTE toc 
		WHERE toc.INCLUDED_MEASURE = 1 
			AND OPERATION_DATE < 
				(SELECT MAX(stoc.OPERATION_DATE) 
					FROM ThickOps_CTE stoc 
					WHERE stoc.TML = toc.TML AND stoc.INCLUDED_MEASURE = 1 
					GROUP BY stoc.TML)
		GROUP BY toc.TML
	) PrevDate ON PrevDate.TML = pt.ID
	LEFT JOIN (
		SELECT 
			toc.TML
			,MIN(OPERATION_DATE) AS 'Date' 
		FROM ThickOps_CTE toc 
		WHERE toc.INCLUDED_MEASURE = 1 
		GROUP BY toc.TML
	) FirstDate ON FirstDate.TML = pt.ID
WHERE 
	ptti.AVALUE LIKE 'MIN'
	
	---- Driving CML logic
	AND 
	(
		(
			ptti.EARLIEST_EXPIRY_DATE = WorstExpiryDate.EarliestED
			AND (ptti.MAX_CORROSION_RATE = HighestCR.HighestCR OR ptti.MAX_CORROSION_RATE IS NULL)
		)
		OR
		(
			DriverCount IS NULL AND NOT ptti.EARLIEST_EXPIRY_DATE IS NULL
		)
	)
	
	---- Example of pulling data for a specific asset
	AND pt.PLACE_ID = {assetIds}

GROUP BY pt.PLACE_ID, bp.NAME, ptti.EARLIEST_EXPIRY_DATE, pt.COMPONENT_ID, pt.NAME, 
	ptti.CORROSION_RATE_T, ptti.CORROSION_RATE_LT_T,
	ptti.ST_CORROSION_RATE, ptti.LT_CORROSION_RATE,
	ptti.NOMINAL_THICKNESS_T, ptti.THICKNESS_LIMIT_T,
	LastDate.Date, PrevDate.Date, FirstDate.Date, pt.ID, bp.ID
	
---- If CMLs have identical CRs and EDs, then only select 1 for a given asset
---- This ORDER BY is necessary for TOP 1 WITH TIES to work properly
ORDER BY ROW_NUMBER() OVER (PARTITION BY bp.ID ORDER BY bp.ID)
;


                ;"
                );
            await connection.CloseAsync();
            return generalAnalysisDetails.ToList();

        }


        public async Task<List<InspectionAttachments>> GetInspectionAttachments(string operationid)
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);

            await connection.OpenAsync();
            var inspectionAttachments = await connection.QueryAsync<InspectionAttachments>($@"
               select 
                dm.DOCUMENT_ID AS DOCUMENTID,
                ds.OBJECTID as INSPECTIONID,
                dm.DESCRIPTION AS DESCRIPTION,
                dm.DOCUMENTNAME as FILENAME,
                coalesce(dm.CONTENTTYPE,dm.DOCUMENTNAME) AS FILETYPE ,
				bdg.DESCRIPTION AS DOCUMENTTYPE,
                dm.CREATIONDATE AS CREATIONDATE,
                dm.DOCUMENTPATH AS FILELINK,
                po.PLACE_ID AS ASSETID
                from DOCUMENTS_METADATA dm
                join DOCUMENTS_ASSOCIATION ds on ds.DOCUMENTID = dm.DOCUMENT_ID
				join PLAN_OPERATION po on ds.OBJECTID =po.ID
                left join BASE_DOC_GROUP bdg on bdg.ID = dm.DOCUMENTSGROUP
                where ds.OBJECTID IN  ({operationid})
                ");
            //--(select ID from PLAN_OPERATION where PLACE_ID IN({ assetIds})
            await connection.CloseAsync();
            return inspectionAttachments.ToList();

        }
        public async Task<List<AnteaAsset>> GetAssestsByIds(string assetIds)
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);

            await connection.OpenAsync();
            var assets = await connection.QueryAsync<AnteaAsset>($@"
                        SELECT
    bp.ID AS ID, --ID
	kvd1.VALUE_ AS  LINK,
   dm.DOCUMENTPATH AS IMAGE,
    IIF(LEFT(REVERSE(kvd.LISTOFS_VAL), 1) = ',',
       LEFT(kvd.LISTOFS_VAL, LEN(kvd.LISTOFS_VAL) - 1),
       kvd.LISTOFS_VAL) AS RiskClass, --RiskClass
        CASE
           WHEN bp.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy'
                AND EXISTS (SELECT 1
                            FROM BOM_PLACE_ANCESTORS anc
                            LEFT JOIN BOM_PLACE abp ON abp.ID = anc.ANCESTOR
                            WHERE anc.DESCENDENT = bp.ID
                            AND abp.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LineNumberPlaceStrategy')
           THEN
               (SELECT TOP 1 CONCAT(abp.NAME, ': ', bp.NAME)
                FROM BOM_PLACE_ANCESTORS anc
                LEFT JOIN BOM_PLACE abp ON abp.ID = anc.ANCESTOR
                WHERE anc.DESCENDENT = bp.ID
                AND abp.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LineNumberPlaceStrategy')
           ELSE bp.NAME
       END AS ASSETID,
    bcf.DESCRIPTION AS ASSETTYPE, -- ASSET TYPE
    COALESCE(bps.SERVICE, basef.DESCRIPTION) AS SERVICE, --SERVICE
    bp.DESCRIPTION AS DESCRIPTION, -- description
     (SELECT abp.NAME
        FROM BOM_PLACE_ANCESTORS anc
        LEFT JOIN BOM_PLACE abp ON abp.ID = anc.ANCESTOR
        WHERE anc.DESCENDENT = bp.ID
        AND STRATEGYCLASSNAME = 'it.imc.persistence.po.bom.PUnitPlaceStrategy') AS 'AREANAME',
       bp.STRATEGYCLASSNAME AS SCLASS,
    bp.PARENT_ID AS AREAID,
     CASE 
        WHEN  bp.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LineNumberPlaceStrategy' OR  bp.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy' THEN 'Piping'
        WHEN bp.STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.TankPlaceStrategy' THEN 'Tanks'
        ELSE 'Vessels'
    END AS ASSETCATEGORY, --ASSET CATEGORY
    CASE 
       WHEN STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.EquipmentPlaceStrategy'  THEN 'Vessel'
       WHEN STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.TankPlaceStrategy' THEN 'Tank'
       WHEN STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.PlantsBoilerPlaceStrategy' THEN 'Boiler'
       WHEN STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.FurnacePlaceStrategy' THEN 'Furnace'
       WHEN STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.PsvValvePlaceStrategy'   THEN 'PSV'
       WHEN STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.BurstingDiscPlaceStrategy'    THEN 'Rupture Disc'
       WHEN STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy'  THEN 'Piping'
       WHEN STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LineNumberPlaceStrategy'  THEN 'Piping'
    END AS EQUIPCATEGORY, --EQUIP CATEGORY
   COALESCE (bce.ORIENTATION,pcb.ORIENTATION,pcf.ORIENTATION,pct.ORIENTATION )AS ORIENTATION, --Orientation
    CASE 
        WHEN STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LinePlaceStrategy' OR 
        STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.LineNumberPlaceStrategy' THEN (SELECT LISTOFS_VAL FROM KV_DEFENTRY WHERE PKV_TEMPLATE = '456927642983985152' AND OBJ_ID = bp.ID)
        ELSE
        baseec.DESCRIPTION
    END AS 'CONSTRUCTIONCODE',
    --COALESCE(
    --    baseec.DESCRIPTION, 
    --    (SELECT LISTOFS_VAL FROM KV_DEFENTRY WHERE PKV_TEMPLATE = '456927642983985152' AND OBJ_ID = bp.ID)
    --) AS CONSTRUCTIONCODE, --construction code
    baseic.DESCRIPTION AS INSPECTIONCODE, --inspection code
    pi.SUBJECT_NAME AS MANUFACTURER, -- Manufacturer
    YEAR(bcs.CONSTRUCTION_CALENDAR) AS CONSTRUCTIONYEAR, -- construction year
    COALESCE(bce.ELEMENTDESC,pcb.ELEMENTDESC,pcf.ELEMENTDESC,pct.ELEMENTDESC,basenom.DESCRIPTION) AS DIMENSIONS, -- DIMENSIONS
    bcs.SERIAL_NUMBER AS SERIALNUMBER,-- serial number
    (SELECT VALUE_ FROM KV_DEFENTRY WHERE PKV_TEMPLATE = '456926744295632896' AND OBJ_ID = bp.ID) AS NATIONALBOARD, --National board
    (SELECT VALUE_ FROM KV_DEFENTRY WHERE PKV_TEMPLATE = '456926887162015744' AND OBJ_ID = bp.ID) AS LOCALJURIDICTIONAL, --Local juridictional
    (SELECT VALUE_ FROM KV_DEFENTRY WHERE PKV_TEMPLATE = '456929211351691264' AND OBJ_ID = bp.ID) AS PID,
    (SELECT VALUE_ FROM KV_DEFENTRY WHERE PKV_TEMPLATE = '632253860060598272' AND OBJ_ID = bp.ID) AS CMMS
    
FROM BOM_PLACE bp
LEFT JOIN BOM_PLACE_STR bps ON bps.ID = bp.ID
LEFT JOIN BOM_COMP_STR bcs ON bcs.ID = bp.COMPONENT_ID
LEFT JOIN BOM_PLACE_LINE bpl ON bpl.ID = bp.ID
LEFT JOIN BOM_PLACE_LINENUMBER bpln ON bpln.ID = bp.ID
LEFT JOIN BOM_COMP_EQUIPMENT bce ON bce.ID = bp.COMPONENT_ID
LEFT JOIN PLANTS_COMP_BOILER pcb ON pcb.ID = bp.COMPONENT_ID
LEFT JOIN PLANTS_COMP_FURNACE pcf ON pcf.ID = bp.COMPONENT_ID
LEFT JOIN PLANTS_COMP_TANK pct ON pct.ID = bp.COMPONENT_ID
LEFT JOIN 
    BASE_INSPECTION_CLASS baseic 
    ON baseic.ID = COALESCE(
        bce.INSPECTION_CLASS, 
        pcb.INSPECTION_CLASS, 
        pcf.INSPECTION_CLASS, 
        pct.INSPECTION_CLASS, 
        bpl.INSPECTION_CLASS, 
        bpln.INSPECTION_CLASS)
--LEFT JOIN BASE_EQUIP_CATEGORY bec ON bec.ID = bce.EQUIP_CATEGORY 
LEFT JOIN BASE_COMP_FAMILY bcf ON bcf.ID = bcs.COMPONENT_FAMILY
LEFT JOIN BASE_EQUIP_CLASS baseec ON baseec.ID = COALESCE(bce.EQUIP_CLASS,pcb.EQUIP_CLASS,pcf.EQUIP_CLASS,pct.EQUIP_CLASS)
LEFT JOIN BASE_FLUID basef ON basef.ID = bpl.FLUID
LEFT JOIN BASE_NOMDIAM basenom ON basenom.ID = bpl.NOMDIAM
LEFT JOIN PB_ROLE pb ON pb.ID = bcs.MANUFACTURER
LEFT JOIN PB_ITEM pi ON pb.ITEM_ID = pi.ID
LEFT JOIN DOCUMENTS_METADATA dm ON dm.DOCUMENT_ID = bp.ASSET_PHOTO
LEFT JOIN KV_DEFENTRY kvd1 
    ON kvd1.OBJ_ID = bp.ID 
    AND kvd1.PKV_TEMPLATE = (
        SELECT bpkv.ID 
        FROM BASE_PLACEKVTEMPLATE bpkv 
        WHERE bpkv.CODE = 'Digital Twin'
    )
LEFT JOIN KV_DEFENTRY kvd  ON kvd.OBJ_ID = bp.ID  
               AND kvd.PKV_TEMPLATE = (SELECT bpkv.ID  
               FROM BASE_PLACEKVTEMPLATE bpkv  
               WHERE bpkv.CODE = 'Risk Class')
WHERE bp.STRATEGYCLASSNAME IN (
    'it.imc.persistence.po.plants.place.LineNumberPlaceStrategy',
    'it.imc.persistence.po.plants.place.PlantsBoilerPlaceStrategy',
    'it.imc.persistence.po.plants.place.FurnacePlaceStrategy',
    'it.imc.persistence.po.plants.place.TankPlaceStrategy',
    'it.imc.persistence.po.plants.place.EquipmentPlaceStrategy',
    'it.imc.persistence.po.plants.place.LinePlaceStrategy'
)
                and bp.ID IN ({assetIds})
");
            await connection.CloseAsync();
            return assets.ToList();

        }
        public async Task<List<EquipementIdStrategies>> GetEquipmentIds(string locationId)
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);

            await connection.OpenAsync();
            var equipmentIds = await connection.QueryAsync<EquipementIdStrategies>($@"
                WITH BOM_Hierarchy AS (
                    -- Anchor member: start with the given location ID
                    SELECT 
                        ID,
                        NAME,
                        STRATEGYCLASSNAME,
                        PARENT_ID,
                        COMPONENT_ID
                    FROM 
                        BOM_PLACE
                    WHERE 
                        ID = {locationId}
    
                    UNION ALL
    
                    -- Recursive member: get all children of the current node
                    SELECT 
                        b.ID,
                        b.NAME,
                        b.STRATEGYCLASSNAME,
                        b.PARENT_ID,
                        b.COMPONENT_ID
                    FROM 
                        BOM_PLACE b
                    INNER JOIN 
                        BOM_Hierarchy bh ON bh.ID = b.PARENT_ID
                ) select ID, STRATEGYCLASSNAME from BOM_Hierarchy where STRATEGYCLASSNAME NOT IN 
                ('it.imc.persistence.po.plants.place.GroupPlaceStrategy',
                'it.imc.persistence.po.bom.PUnitPlaceStrategy');
                ");
            await connection.CloseAsync();
            return equipmentIds.ToList();

        }
        public async Task<List<AssetManagementSites>> GetAllAssetManagementSitesAsync()
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);

            await connection.OpenAsync();

            var assetManagementSites = await connection.QueryAsync<AssetManagementSites>(@"
             SELECT DISTINCT abp.NAME AS LOCATIONNAME, abp.ID  AS LOCATIONID
             FROM BOM_PLACE_ANCESTORS anc
             LEFT JOIN BOM_PLACE abp ON abp.ID = anc.ANCESTOR
             LEFT JOIN BOM_PLACE_STR bps ON bps.ID = abp.ID
             LEFT JOIN BASE_COMP_FAMILY bcf ON bcf.ID = bps.COMPONENT_FAMILY
             WHERE STRATEGYCLASSNAME = 'it.imc.persistence.po.plants.place.GroupPlaceStrategy'
             AND bcf.DESCRIPTION = 'OIS LOCATION' -- Switch to 'OIS CLIENT' for Client list
             ORDER BY abp.NAME;
             ");


            await connection.CloseAsync();
            return assetManagementSites.ToList();

        }
        public async Task<string> GetCostCenter(string locationId)
        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();
            var clientFacility = await connection.QueryAsync<string>(@$"
                select BASE_PLACECOSTCENTER.DESCRIPTION from BOM_PLACE_STR 
	                join BASE_PLACECOSTCENTER on BASE_PLACECOSTCENTER.ID = BOM_PLACE_STR.PLACECOSTCENTER
	                where BOM_PLACE_STR.ID = {locationId}
                ");
            await connection.CloseAsync();
            return clientFacility.ToList().Count > 0 ? clientFacility.ToList()[0] : "";

        }
        public async Task<List<SystemManagementCategories>> GetSystemManagementCategories()

        {
            var connection = new SqlConnection(_dataOptions.Value.ConnectionString);
            await connection.OpenAsync();
            var systemCategories = await connection.QueryAsync<SystemManagementCategories>(@"
            select CODE,DESCRIPTION AS SYSTEMCATEGORY
            from BASE_MANAGEMENT_SYSTEM_CAT 
            where ID <> '448761391094820864' or DESCRIPTION <> 'Kyoto'
            ");
            await connection.CloseAsync();
            return systemCategories.ToList();
        }
        public async Task<bool> UploadSubmissionFilesToGCPBucket(SubmissionFileUpload submissionFile)
        {
            var client = StorageClient.Create();
            var file = Convert.FromBase64String(submissionFile.FileData);
            var obj = await client.UploadObjectAsync(
                _anteaSubmissionsBucketName,
                submissionFile.FileName,
                submissionFile.FileType,
                new MemoryStream(file)
                );
            return true;
        }

        public async Task<bool> UploadSubmissionFilesToAzureBlob(SubmissionFileUpload submissionFile)
        {
            try
            {
                // TODO: Add Azure Blob Storage configuration
                // For now, return true to avoid compilation errors
                // This method needs to be implemented with proper Azure Blob Storage logic
                await Task.CompletedTask;
                return true;
            }
            catch
            {
                return false;
            }
        }
        public async Task<List<ResponseSubmissions>> GetSubmissionsAsync(string assetIdFilter)
        {
            CollectionReference submissionsRef = _firestoreDb1.Collection("OIS-Submissions");
            QuerySnapshot snapshot = await submissionsRef.GetSnapshotAsync();
            List<ResponseSubmissions> submissions = new List<ResponseSubmissions>();
            foreach (DocumentSnapshot document in snapshot.Documents)
            {
                if (document.Exists)
                {
                    Dictionary<string, object> docData = document.ToDictionary();
                    string anomalyId = null;
                    string assetId = null;
                    bool assetFound = false;
                    if (docData.ContainsKey("AnomalyInfo") && docData["AnomalyInfo"] != null)
                    {
                        var anomalyInfo = (Dictionary<string, object>)docData["AnomalyInfo"];
                        anomalyId = anomalyInfo.ContainsKey("AnomalyNumber") && anomalyInfo["AnomalyNumber"] != null ? anomalyInfo["AnomalyNumber"].ToString() : null;
                    }
                    if (docData.ContainsKey("AssetInfo") && docData["AssetInfo"] != null)
                    {
                        var assetInfoList = (List<object>)docData["AssetInfo"]; // Cast to List<object> instead of object[]
                        foreach (var assetInfoObj in assetInfoList)
                        {
                            var assetInfo = (Dictionary<string, object>)assetInfoObj;
                            if (assetInfo.ContainsKey("AssetId") && assetInfo["AssetId"] != null)
                            {
                                assetId = assetInfo["AssetId"].ToString();
                                if (assetId == assetIdFilter)
                                {
                                    assetFound = true; // Set flag if the assetId matches the filter
                                    break;
                                }
                            }
                        }
                    }
                    if (!assetFound)
                    {
                        continue;
                    }
                    ResponseSubmissions submission = new ResponseSubmissions
                    {
                        Id = document.Id,
                        CreatedDate = docData.ContainsKey("CreatedDate") && docData["CreatedDate"] != null ? docData["CreatedDate"].ToString() : null,
                        SubmissionType = docData.ContainsKey("SubmissionType") && docData["SubmissionType"] != null ? docData["SubmissionType"].ToString() : null,
                        AnomalyId = anomalyId,
                        ClientClosedDate = docData.ContainsKey("ClientClosedDate") && docData["ClientClosedDate"] != null ? docData["ClientClosedDate"].ToString() : null,
                        Comment = docData.ContainsKey("Comment") && docData["Comment"] != null ? docData["Comment"].ToString() : null,
                        AssetId = assetId,
                        // ServiceType = docData.ContainsKey("ServiceType") && docData["ServiceType"] != null ? docData["ServiceType"].ToString() : null,
                        SubmittedUser = docData.ContainsKey("SubmittedUser") && docData["SubmittedUser"] != null ? docData["SubmittedUser"].ToString() : null,
                    };
                    if (submission.SubmissionType == "Document Upload")
                    {
                        // Count documents if present in the document data
                        int documentCount = 0;
                        if (docData.ContainsKey("Documents") && docData["Documents"] is List<object> documentList)
                        {
                            documentCount = documentList.Count;
                        }
                        submission.ServiceType = $"{documentCount} documents";
                    }
                    else if (submission.SubmissionType == "Anomaly Update")
                    {
                        submission.ServiceType = anomalyId;
                    }
                    else
                    {
                        // Default ServiceType value
                        submission.ServiceType = docData.ContainsKey("ServiceType") && docData["ServiceType"] != null ? docData["ServiceType"].ToString() : null;
                    }

                    submissions.Add(submission);
                }
            }
            return submissions;
        }
        public async Task<string> SendSubmissionMail(Submissions submissions, List<FireStoreSubmissionFileUpload> firestoreDocuments, IEmailService emailService)
        {
            try
            {
                string bucketName = _anteaSubmissionsBucketName;
                // Need to remove in Prod
                List<IEmailRecipient> emailRecipients = new List<IEmailRecipient>
                {
                    new EmailRecipient
                    {
                        Id = "<EMAIL>",
                        Surname = "Korni",
                        GivenName = "Manideep"
                    }

                };
                EmailRecipient aioTeamRecipient = new EmailRecipient
                {
                    Id = "<EMAIL>",
                    Surname = "OneInsight",
                    GivenName = "TEAM AIOM"
                };
                EmailData emailData = new EmailData();
                //Mail to Client
                if (submissions.SenderInfo != null)
                {
                    emailData.Subject = "Submission Complete for " + submissions.SubmissionType;
                    emailData.HtmlContent = (await CreateHeaderLogoHTML())
                            + $@"Hello {submissions.SenderInfo.GivenName}, " + await CreateHTMLBodyContentForSubmissionForClient(submissions)
                            + (await CreateFooterHTML());

                    List<IEmailRecipient> _emailRecipient = new List<IEmailRecipient>
                    {
                        new EmailRecipient()
                        {
                        Id=submissions.SenderInfo.EmailId,
                        GivenName=submissions.SenderInfo.GivenName,
                        Surname=submissions.SenderInfo.Surname
                        },

                    };
                    emailData.Recipients = _emailRecipient;
                    var emailResponse = await emailService.SendEmail(emailData);

                }

                // Mail for AIO Team
                if (aioTeamRecipient != null)
                {
                    emailData.Subject = "New OIS Submission - " + submissions.SubmissionType;
                    var _documentNames = GenerateDocumentHtmlLinks(firestoreDocuments, bucketName);
                    SubmissionsJsonFile submissionsJsonFileData = new SubmissionsJsonFile()
                    {

                        Id = submissions.Id,
                        ClientClosedDate = submissions.ClientClosedDate,
                        ClientFacility = submissions.ClientFacility,
                        CreatedDate = submissions.CreatedDate,
                        SubmissionType = submissions.SubmissionType,
                        AnomalyInfo = submissions.AnomalyInfo,
                        Comment = submissions.Comment,
                        AssetInfo = submissions.AssetInfo,
                        ServiceType = submissions.ServiceType,
                        SenderInfo = submissions.SenderInfo,
                        Documents = GenerateDocumentJsonAttachments(firestoreDocuments, bucketName),
                        CostCenter = submissions.CostCenter
                    };
                    string json = JsonConvert.SerializeObject(submissionsJsonFileData, Formatting.Indented);
                    MemoryStream jsonFileStream = new MemoryStream(Encoding.UTF8.GetBytes(json));
                    List<EmailAttachment> emailAttachments = new List<EmailAttachment> { new EmailAttachment {
                    FileName = "Submissiondata.json",
                    Stream = jsonFileStream } };
                    emailData.Attachments = emailAttachments;
                    emailData.HtmlContent = (await CreateHeaderLogoHTML()) + await CreateHTMLBodyContentForSubmissionForAIOTeam(submissions, _documentNames) + (await CreateFooterHTML());
                    List<IEmailRecipient> _emailRecipient = new List<IEmailRecipient>
                    {
                       aioTeamRecipient

                    };
                    emailData.Recipients = _emailRecipient;
                    var emailResponse = await emailService.SendEmail(emailData);
                }
                return "Success";
            }
            catch (Exception e)
            {
                return "Error while sending email:: " + e.Message.ToString();
            }
        }

        private async Task<string> CreateHTMLBodyContentForSubmissionForClient(Submissions submissions)
        {
            var bodyHtmlContent = "";
            var _documentNames = "";
            var _assetnameDescriptions = "";
            var _assetNames = "";
            if (submissions.Documents.Count != 0)
            {
                _documentNames = String.Join(", ", submissions.Documents.Select(u => u.FileName));
            }
            if (submissions.AssetInfo.Count != 0)
            {
                foreach (var asset in submissions.AssetInfo)
                {
                    _assetnameDescriptions += $" {asset?.AssetName}, {asset?.AssetDescription} ; ";
                    _assetNames += $" {asset?.AssetName},";
                }
            }
            if (submissions.SubmissionType.ToLower() == "anomaly update")
            {
                bodyHtmlContent = $@"<p>Your Anomaly Update <strong>{submissions?.AnomalyInfo.AnomalyNumber}, for {_assetNames}</strong> has been successfully uploaded into the system. Our Processing Team will review the changes and let you know if we have any further questions.</p>
                        <p>No further action is required from you, but if you wish to review your update, you can access it by logging in to <a href='https://digital.teaminc.com/#/' style='color: #004b8d;'>TEAM OneInsight</a>, and navigating to the asset related to your upload. A log of all submissions, per asset, can be found on the Equipment “Submissions” details tab, and the Anomalies and Recommendations Dashboard has been updated to reflect the change.</p>
                        <strong>Asset:</strong> {_assetnameDescriptions}
                        <br>
                        <strong>Anomaly Priority and Type:</strong> {submissions?.AnomalyInfo.AnomalyPriority ?? ""}, {submissions?.AnomalyInfo.AnomalyType ?? ""}
                        <br>
                        <strong>Observation Type and Date:</strong> {submissions?.AnomalyInfo.AnomalyInspectionOperation ?? ""}
                        <br>
                        <strong>Date Closed per Client:</strong> {submissions?.ClientClosedDate ?? ""}
                        <br>
                        <strong>Document(s):</strong> {_documentNames}
                        <br>
                        <strong>Submitted By:</strong> {submissions?.SenderInfo.GivenName ?? ""}  {submissions?.SenderInfo.Surname ?? ""}
                        <br>
                       <div style=""display: flex;"">
                        <strong >Anomaly Update Comment:&nbsp;</strong> {submissions?.Comment ?? ""} 
                             </div>  
                        </div>
                        <p>Thank you,<br>
                           TEAM AIOM OneInsight <br>
                           Asset Integrity Optimization & Management
                        </p>
                            ";
                return bodyHtmlContent;

            }
            else if (submissions.SubmissionType.ToLower() == "document upload")
            {
                bodyHtmlContent = $@"
                    <p>Your document(s), {_documentNames}, has been successfully uploaded into the system. No further action is required from you. If you would like to review your submission, you can access it by logging in to <a href='https://digital.teaminc.com/#/' style='color: #004b8d;'>TEAM OneInsight</a>, and navigating to the asset related to your upload. A log of all submissions, per asset, can be found on the Equipment “Submissions” details tab.</p>                

                    <div>
                    <strong>Asset:</strong> {_assetnameDescriptions}
                    <br>
                    <strong>Document(s):</strong> {_documentNames}
                    <br>
                    <strong>Submitted By:</strong> {submissions?.SenderInfo.GivenName ?? ""} {submissions?.SenderInfo.Surname ?? ""}
                    <br>
                   <div style=""display: flex;"">
                    <strong>Upload Comment:&nbsp;</strong> {submissions?.Comment ?? ""}
                        </div> 
                    </div>
                    <p>Thank you,<br>
                           TEAM AIOM OneInsight <br>
                            Asset Integrity Optimization & Management
                        </p>";
                return bodyHtmlContent;
            }
            else if (submissions.SubmissionType.ToLower() == "request service")
            {
                bodyHtmlContent = $@"
                    <p>Your Request for <strong>{submissions.ServiceType}</strong> has been successfully received. A member of our team will reach out shortly to discuss and schedule the requested service. No further action is required from you at this time.<br><br>                
                    If an asset was associated with your request and you would like to review your submission, you can access it by logging in to <a href='https://digital.teaminc.com/#/' style='color: #004b8d;'>TEAM OneInsight</a>, and navigating to the asset related to your request. A log of all submissions, per asset, can be found on the Equipment “Submissions” details tab.</p>
                    <div>
                    <strong>Service Request Type:</strong> {submissions.ServiceType}
                    <br>
                    <strong>Asset:</strong> {_assetnameDescriptions}
                    <br>
                    <strong>Document(s):</strong> {_documentNames}
                    </br>
                    <strong>Submitted By:</strong> {submissions?.SenderInfo.GivenName ?? ""} {submissions?.SenderInfo.Surname ?? ""}
                    </br>
                    <div style=""display: flex;"">
                    <strong>Service Request Comment:&nbsp;</strong> {submissions?.Comment ?? ""} 
                        </div>
                           </div>
                     <p>Thank you,<br>
                           TEAM AIOM OneInsight <br>
                            Asset Integrity Optimization & Management
                        </p>
                        "
                    ;
                return bodyHtmlContent;
            }
            else
            {
                return bodyHtmlContent;
            }
        }
        private async Task<string> CreateHTMLBodyContentForSubmissionForAIOTeam(Submissions submissions, string _documentNames)
        {
            var bodyHtmlContent = "";
            var _assetnameDescriptions = "";
            var _assetNames = "";
            if (submissions.AssetInfo.Count != 0)
            {
                foreach (var asset in submissions.AssetInfo)
                {
                    _assetnameDescriptions += $" {asset?.AssetName}, {asset?.AssetDescription} ; ";
                    _assetNames += $" {asset?.AssetName},";
                }
            }
            if (submissions.SubmissionType.ToLower() == "anomaly update")
            {

                bodyHtmlContent = $@"<p> New Anomaly update.</p>
                        <strong>Client Facility:</strong> {submissions?.ClientFacility}
                        <br>
                        <strong>Cost Center:</strong> {submissions?.CostCenter}
                        <br>
                        <strong>Asset:</strong> {_assetnameDescriptions}
                        <br>
                        <strong>Anomaly Number:</strong> {submissions?.AnomalyInfo.AnomalyNumber ?? ""}
                        <br>
                        <strong>Anomaly Priority and Type:</strong> {submissions?.AnomalyInfo.AnomalyPriority ?? ""}, {submissions?.AnomalyInfo.AnomalyType ?? ""}
                        <br>
                        <strong>Observation Type and Date</strong> {submissions?.AnomalyInfo.AnomalyInspectionOperation ?? ""}
                        <br>
                        <strong>Date Closed per Client:</strong> {submissions?.ClientClosedDate ?? ""}
                        <br>
                        <strong>Document(s):</strong> {_documentNames}
                        <br>
                        <strong>Submitted By:</strong> {submissions?.SenderInfo.GivenName ?? ""}  {submissions?.SenderInfo.Surname ?? ""}
                        <br>
                         <div style=""display: flex;"">
                        <strong >Anomaly Update Comment:&nbsp;</strong><span>{submissions?.Comment ?? ""} </span>
                             </div>  
                        </div>";
                return bodyHtmlContent;

            }
            else if (submissions.SubmissionType.ToLower() == "document upload")
            {

                bodyHtmlContent = $@"
                    <p>New Documents Uploaded.</p>                
                    <div>
                    <strong>Client Facility:</strong> {submissions?.ClientFacility}
                    <br>
                    <strong>Cost Center:</strong> {submissions?.CostCenter}
                    <br>
                    <strong>Asset:</strong> {_assetnameDescriptions}
                    <br>
                    <strong>Document(s):</strong> {_documentNames}
                    <br>
                    <strong>Submitted By:</strong> {submissions?.SenderInfo.GivenName ?? ""} {submissions?.SenderInfo.Surname ?? ""}
                    <br>
                    <div style=""display: flex;"">
                    <strong>Upload Comment:&nbsp;</strong><span>{submissions?.Comment ?? ""}</span>
                        </div>  
                    </div>";
                return bodyHtmlContent;
            }
            else if (submissions.SubmissionType.ToLower() == "request service")
            {

                bodyHtmlContent = $@"
                    <p>New Service Request.</p>
                    <div>
                    <strong>Client Facility:</strong> {submissions?.ClientFacility}
                    <br>
                    <strong>Cost Center:</strong> {submissions?.CostCenter}
                    <br>
                    <strong>Service Request Type:</strong> {submissions.ServiceType}
                    <br>
                    <strong>Asset:</strong> {_assetnameDescriptions}
                    <br>
                    <strong>Document(s):</strong> {_documentNames}
                    </br>
                    <strong>Submitted By:</strong> {submissions?.SenderInfo.GivenName ?? ""} {submissions?.SenderInfo.Surname ?? ""}
                    </br>
                    <div style=""display: flex;"">
                    <strong>Service Request Comment:&nbsp;</strong> <span> {submissions?.Comment ?? ""} </span>
                        </div>  
                           </div>"
                    ;
                return bodyHtmlContent;
            }
            else
            {
                return bodyHtmlContent;
            }
        }
        private string GenerateDocumentHtmlLinks(List<FireStoreSubmissionFileUpload> _firestoreDocuments, string bucketName)
        {
            if (_firestoreDocuments.Count == 0)
                return string.Empty;

            var sb = new StringBuilder();

            foreach (var doc in _firestoreDocuments)
            {
                string urlEncodedFileName = Uri.EscapeDataString(doc.FileName);
                string gcsUrl = $"https://storage.cloud.google.com/{bucketName}/{urlEncodedFileName}";

                // Extract the link name by removing the GUID and taking the remainder
                string linkName = ExtractLinkName(doc.FileName);

                sb.AppendLine($"<a href='{gcsUrl}' style='color: #004b8d;'>{linkName}</a>");
            }
            return sb.ToString();

        }
        private List<SubmissionJsonDocumentData> GenerateDocumentJsonAttachments(List<FireStoreSubmissionFileUpload> _firestoreDocuments, string bucketName)
        {
            if (_firestoreDocuments.Count == 0)
                return null;

            var attachments = new List<SubmissionJsonDocumentData>();
            foreach (var doc in _firestoreDocuments)
            {
                string urlEncodedFileName = Uri.EscapeDataString(doc.FileName);
                string gcsUrl = $"https://storage.cloud.google.com/{bucketName}/{urlEncodedFileName}";

                // Extract the link name by removing the GUID and taking the remainder
                string filename = ExtractLinkName(doc.FileName);

                attachments.Add(new SubmissionJsonDocumentData
                {
                    FileName = filename,
                    FileLink = gcsUrl
                });
            }
            return attachments;

        }
        public static string ExtractLinkName(string fileName)
        {
            // Split the file name by the underscore and return the part after the GUID
            string[] parts = fileName.Split(new[] { '_' }, 2);
            return parts.Length > 1 ? parts[1] : fileName;
        }
        private async Task<string> CreateHeaderLogoHTML()
        {
            var teamlogoBase64String = "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";
            var oneInsightLogo = Path.Combine(AppDomain.CurrentDomain.BaseDirectory ??
                throw new InvalidOperationException("Unable to obtain AppDomain.CurrentDomain.BaseDirectory"),
                "Files/OneInsight-Final.png");

            var oneInsightlogoBase64String = Convert.ToBase64String(await File.ReadAllBytesAsync(oneInsightLogo));
            var headerHtmlContent = $@"
                 <table style=""width: 100%; margin: auto; border-collapse: collapse;"">
                        <tr>
                            <td style=""padding: 10px; padding-left:0px"">
                                <img src=""data:image/png;base64, {teamlogoBase64String}"" alt=""TEAM Logo"" style=""height: 50px;"">
                                <img src=""data:image/png;base64, {oneInsightlogoBase64String}"" alt=""OneInsight Logo"" style=""height: 50px;"">
                            </td>
                        </tr>
                </tr>
                </table>";
            return headerHtmlContent;
        }
        private async Task<string> CreateFooterHTML()
        {
            var footerHtmlContent = $@"
                <div style=' color: #666666; font-size: 12px;'>
                    <p style='font-size: 13px;'>For technical assistance, please contact TEAM OneInsight Support via email <a href='mailto:<EMAIL>' style='color: #004b8d;'>OneInsight Support</a> or by calling 1-281-388-5600.</p>
                    <p style='text-align:center'><strong>CONFIDENTIALITY NOTICE</strong></p>
                    <p>The information contained in this transmission is confidential. It may also be legally privileged. It is intended only for the addressee(s) stated above. If you are not an addressee, you should not disclose, copy, circulate, or in any other way use the information contained in this transmission. Such unauthorized use may be unlawful. If you have received this transmission in error, please contact us immediately so that we can arrange for its return.</p>
                </div>";
            return footerHtmlContent;
        }


    }
    internal class EmailRecipient : IEmailRecipient
    {
        public string Id { get; set; }
        public string Surname { get; set; }
        public string GivenName { get; set; }
    }
}
