using System;
using Newtonsoft.Json;

namespace ClientPortal.Shared.Models
{
    public class ChangeEvent: ICosmosEntity<string>
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("old")]
        public object Old { get; set; }

        [JsonProperty("new")]
        public object New { get; set; }

        [<PERSON>son<PERSON>roperty("user")]
        public object User { get; set; }

        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("_etag")]
        public string ETag { get; set; }
    }
}