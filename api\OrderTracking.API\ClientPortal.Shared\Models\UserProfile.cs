using System;
using System.Collections.Generic;
using Dapper.Contrib.Extensions;
using Newtonsoft.Json;


namespace ClientPortal.Shared.Models
{
    public class UserProfile : ICosmosEntity<string>, IEmailRecipient
    {
        private const string ClientDisclaimerVersion = "3";
        private const string TEAMDisclaimerVersion = "2";

        #region Properties

        // TODO: Test This
        [JsonProperty(PropertyName = "active")]

        public bool Active
        {
            get
            {
                if (IsTeamEmployee) return true;

                if (LastVerificationDate == null) return false;

                var oneMonthTimeSpan = new TimeSpan(30, 0, 0, 0);

                if (DateTime.UtcNow - LastVerificationDate > oneMonthTimeSpan) return false;

                return true;
            }
        }        [JsonProperty(PropertyName = "acceptedAgreement")]
        public bool AcceptedAgreement
        {
            get
            {
                var oneYearTimeSpan = new TimeSpan(365, 0, 0, 0);
                if (!IsTeamEmployee)
                    return AcceptedClientDisclaimerVersion == ClientDisclaimerVersion &&
                           DateTime.UtcNow - AcceptedDisclaimerDate < oneYearTimeSpan;
                return AcceptedTeamDisclaimerVersion == TEAMDisclaimerVersion &&
                       DateTime.UtcNow - AcceptedDisclaimerDate < oneYearTimeSpan;
            }
        }

        [JsonProperty(PropertyName = "isTeamEmployee")]
        [Write(false)]
        public bool IsTeamEmployee
        {
            get
            {
                if (Id == null)
                    return false;

                if (Id.ToLower().EndsWith("@teaminc.com")) return true;

                return false;
            }
        }

        [JsonProperty(PropertyName = "acceptedClientDisclaimerVersion")]
        public string AcceptedClientDisclaimerVersion { get; set; }

        [JsonProperty(PropertyName = "acceptedTeamDisclaimerVersion")]
        public string AcceptedTeamDisclaimerVersion { get; set; }

        [JsonProperty(PropertyName = "acceptedDisclaimerDate")]
        public DateTime AcceptedDisclaimerDate { get; set; }

        [JsonProperty(PropertyName = "customerAccounts")]
        public ICollection<string> CustomerAccounts { get; set; } = new List<string>();

        [JsonProperty(PropertyName = "districtIds")]
        public ICollection<string> DistrictIds { get; set; } = new List<string>();

        [JsonProperty(PropertyName = "assetManagementSiteIds")]
        public ICollection<string> AssetManagementSiteIds { get; set; } = new List<string>();

        [JsonProperty(PropertyName = "remoteMonitoringSiteIds")]
        public ICollection<string> RemoteMonitoringSiteIds { get; set; } = new List<string>();

        [JsonProperty(PropertyName = "email")]
        public string Email { get; set; }

        [JsonProperty(PropertyName = "givenName")]
        public string GivenName { get; set; }

        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }

        [JsonProperty(PropertyName = "name")]
        public string Name { get; set; }

        [JsonProperty(PropertyName = "roles")]
        public ICollection<string> Roles { get; set; } = new List<string>();

        [JsonProperty(PropertyName = "surname")]
        public string Surname { get; set; }

        [JsonProperty(PropertyName = "lastLoginDate")]
        public DateTime? LastLoginDate { get; set; }

        [JsonProperty(PropertyName = "lastVerificationDate")]
        public DateTime? LastVerificationDate { get; set; }

        [JsonProperty(PropertyName = "verificationToken")]
        public string VerificationToken { get; set; }

        [JsonProperty(PropertyName = "lastClientPortalVersion")]
        public string LastClientPortalVersion { get; set; }

        [JsonProperty(PropertyName = "selectedBusinessUnit")]
        public string SelectedBusinessUnit { get; set; }

        #endregion
    }
}