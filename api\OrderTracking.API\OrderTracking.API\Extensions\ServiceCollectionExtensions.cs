using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
//using AIMaaS.Models;
//using AIMaaS.Services;
using APMWebDataInterface.ExampleDataModel;
using APMWebDataInterface.Headless;
using Audit.Core;
using Audit.SqlServer;
using Audit.SqlServer.Providers;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Models.MOS;
using ClientPortal.Shared.Services;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Azure.Cosmos;
using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;
using OrderTracking.API.Models.PowerBI;
using OrderTracking.API.Repositories;
using OrderTracking.API.Services;
using OrderTracking.API.Services.APM;
using OrderTracking.API.Services.PowerBI;
using Swashbuckle.AspNetCore.SwaggerGen;
//using TeamDigital.PipelineInspection.APIManager;
using Z.Dapper.Plus;

namespace OrderTracking.API.Extensions
{
    /// <summary>
    ///     Extension methods for ServiceCollection to simplify a web api's Startup class.
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        private static IServiceCollection InitializeCosmosClientInstanceAsync(this IServiceCollection services,
            IConfiguration configuration)
        {
            var account = configuration["Connections:Endpoint"];
            var key = configuration["Connections:AuthKey"];

            // Usage
            var databaseId = configuration["Connections:DatabaseName"];

            // Use only Cosmos DB - remove Firestore dependency
            // var client = new ValidatingFirestoreClient(configuration["Connections:ProjectId"], databaseId);
            // var adapter = new FirestoreClientAdapter(client);
            // services.AddSingleton<IFirestoreClientAdapter>(adapter);

            //to be clean up
            var clientCosmo = new ValidatingCosmosClient(account, key,
               new CosmosClientOptions { ConnectionMode = ConnectionMode.Direct });
            var adapterCosmo = new CosmosClientAdapter(clientCosmo);
            services.AddSingleton<ICosmosClientAdapter>(adapterCosmo);

            return services;
        }

        /// <summary>
        ///     Registers services and configuration options related to send grid so that the API can
        ///     send emails programmatically.
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        private static IServiceCollection AddSendGrid(this IServiceCollection services, IConfiguration configuration)
        {
            if (configuration == null) throw new ArgumentNullException(nameof(configuration));

            var sendGridConfig = configuration.GetSection(nameof(ClientPortal.Shared.Models.SendGrid));
            services.Configure<ClientPortal.Shared.Models.SendGrid>(sendGridConfig);
            services.AddScoped<IEmailService, EmailService>();

            return services;
        }

        /// <summary>
        ///     Add services related to authorization (users, roles, etc.) for the client portal.
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        private static IServiceCollection AddPortalAuthorizationServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            if (configuration == null) throw new ArgumentNullException(nameof(configuration));
            var config = configuration.GetSection("Connections");

            var serviceProvider = services.BuildServiceProvider();

            // Use Cosmos DB instead of Firestore
            var containerFactory = new ContainerFactory(config,
                serviceProvider.GetRequiredService<ICosmosClientAdapter>().GetClient());

            services.AddSingleton<IRolesService>(new RolesService(containerFactory, serviceProvider, configuration));
            services.AddSingleton<IUserProfilesService>(new UserProfilesService(containerFactory, serviceProvider, configuration));
            services.AddScoped<IUserAgreementService, UserAgreementService>();
            return services;
        }

        ///// <summary>
        /////     Add WMO services to perform CRUD operations on Orders
        ///// </summary>
        ///// <param name="services"></param>
        ///// <param name="configuration"></param>
        ///// <returns></returns>
        //private static IServiceCollection AddWhereMyOrderServices(this IServiceCollection services,
        //    IConfiguration configuration)
        //{
        //    //services.AddScoped<IOrdersService, OrdersService>();
        //    services.AddDbContext<OrderContext>(options =>
        //        options.UseSqlServer(configuration.GetSection("SQLConnections")["Orders"]));
        //    services.AddScoped<IClientPortalResultsLoader, ClientPortalResultsLoader>();
        //    services.AddScoped<IOrdersJobsService, OrdersJobsService>();

        //    DapperPlusManager.AddLicense(configuration.GetValue<string>("ZDapperPlus:LicenseName"),
        //        configuration.GetValue<string>("ZDapperPlus:LicenseKey"));

        //    return services;
        //}

        ///// <summary>
        /////     Add EDR service to perform CRUD operations on Equipment Demand Requests
        ///// </summary>
        ///// <param name="services"></param>
        ///// <param name="configuration"></param>
        ///// <returns></returns>
        //private static IServiceCollection AddEquipmentDemandRequestServices(this IServiceCollection services,
        //    IConfiguration configuration)
        //{
        //    if (configuration == null) throw new ArgumentNullException(nameof(configuration));

        //    services.AddSingleton<IEquipmentRequestsService, EquipmentRequestsService>();

        //    return services;
        //}

        /// <summary>
        ///     Adds Azure Blob Storage and creates singleton services for various resources.
        ///     Each resource gets its own singleton service to upload file blobs and associate
        ///     the file's meta data to the resource in question.
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        private static IServiceCollection AddAzureStorageService(this IServiceCollection services,
            IConfiguration configuration)
        {
            if (configuration == null) throw new ArgumentNullException(nameof(configuration));

            var configurationSection = configuration.GetSection(nameof(BlobStorage));
            services.Configure<BlobStorage>(configurationSection);
            //services.AddSingleton<IWMOBlobStorageService, WMOBlobStorageService>();
            //services.AddSingleton<IEDRBlobStorageService, EDRBlobStorageService>();
            services.AddSingleton<IAPMBlobStorageService, APMBlobStorageService>();
            services.AddScoped<ICloudStorageService, CloudStorageService>();
            //services.AddSingleton<IAPMReportingBlobStorageService, APMReportingBlobStorageService>();

            return services;
        }

        private static IServiceCollection AddAzureKeyVaultService(this IServiceCollection services, IConfiguration configuration)
        {
            var keyVaultUri = configuration["BlobStorage:AzureKeyVaultUri"];
            if (!string.IsNullOrEmpty(keyVaultUri))
            {
                services.AddSingleton<SecretClient>(provider =>
                {
                    var credential = new DefaultAzureCredential();
                    return new SecretClient(new Uri(keyVaultUri), credential);
                });
            }
            return services;
        }

        ///// <summary>
        /////     Register injectables for CredoSoft functionality
        ///// </summary>
        ///// <param name="services"></param>
        ///// <param name="configuration"></param>
        ///// <returns></returns>
        //private static IServiceCollection AddCredoSoftServices(this IServiceCollection services,
        //    IConfiguration configuration)
        //{
        //    if (configuration == null) throw new ArgumentNullException(nameof(configuration));

        //    services.Configure<CredoSoftData>(configuration.GetSection(nameof(CredoSoftData)));

        //    services.AddScoped<ICredoSoftService, CredoSoftService>();

        //    return services;
        //}

        private static IServiceCollection AddFieldServiceManagementServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.Configure<FSMConfig>(configuration.GetSection(nameof(FSMConfig)));
            services.AddSingleton<IFieldServiceManagementService, FieldServiceManagementService>();
            return services;
        }

        private static IServiceCollection AddMechanicalAndOnStreamServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddDbContext<MOSContext>();
            return services;
        }

        private static IServiceCollection AddAPMServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Initialize APM services without Firebase dependency
            var driver = APM_WebDataInterface.Global;
            var environment = configuration.GetValue<string>("APM:Environment") == "production"
                ? APM_WebDataInterface.Databases.production
                : APM_WebDataInterface.Databases.testing;

            var cmsConfig = configuration.GetSection("APM:CmsSettings").Get<Dictionary<string, string>>();
            CMSSettings.Initialize(cmsConfig);
            driver.Initialize(environment).Wait();
            services.TryAddSingleton(driver);
            services
                .AddScoped<IJasperSoftService, JasperSoftService>()
                .AddScoped<ILeakReportService, LeakReportService>()
                .AddScoped<IProjectService, ProjectService>()
                .AddScoped<IAssetService, AssetService>()
                .AddScoped<IWorkOrderService, WorkOrderService>()
                .AddScoped<ILocationService, LocationService>()
                .AddScoped<ITasksService, TasksService>();
            return services;
        }

        //private static IServiceCollection AddCRDServices(this IServiceCollection services, IConfiguration configuration)
        //{
        //    Configuration.DataProvider = new SqlDataProvider
        //    {
        //        ConnectionString = configuration.GetConnectionString("CRDDatabase"),
        //        Schema = "dbo",
        //        TableName = "AuditLog",
        //        IdColumnName = "EventId",
        //        JsonColumnName = "JsonData",
        //        LastUpdatedDateColumnName = "LastUpdatedDate",
        //        CustomColumns = new List<CustomColumn>
        //        {
        //            new("EventType", ev => ev.EventType),
        //            new("User", ev => ev.CustomFields["User"]),
        //            new("RecordId", ev => ev.CustomFields["RecordId"])
        //        }
        //    };
        //    services.AddScoped<ICRDService, CRDService>();
        //    return services;
        //}

        /// <summary>
        ///     Add services for Power BI Integration
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        private static IServiceCollection AddPowerBIServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddScoped(typeof(AadService))
                .AddScoped(typeof(PbiEmbedService));
            services.Configure<AzureAd>(configuration.GetSection("AzureAd"))
                .Configure<PowerBI>(configuration.GetSection("PowerBI"));

            return services;
        }

        /// <summary>
        ///     Core method for adding Services for the Portal Application
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection AddPortalApplicationServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            if (configuration == null) throw new ArgumentNullException(nameof(configuration));

            // Binding to CosmosDB Connection settings to give us injectable IOptions<Connections>
            var configurationSection = configuration.GetSection(nameof(Connections));
            services.Configure<Connections>(configurationSection);

            // Add additional module or overall service configuration here.
            // This allows us to keep a single call in Startup to this method
            services.InitializeCosmosClientInstanceAsync(configuration)
                .AddScoped<IReleaseNotesService, ReleaseNotesService>()
                .AddScoped<IAuthHistoryService, AuthHistoryService>()
                .AddPortalAuthorizationServices(configuration)
                //.AddWhereMyOrderServices(configuration)
                //.AddEquipmentDemandRequestServices(configuration)
                .AddAzureStorageService(configuration)
                .AddAzureKeyVaultService(configuration)
                .AddApplicationInsights(configuration)
                //.AddCredoSoftServices(configuration)
                //.AddPipelineInspectionServices(configuration)
                .AddFlangeCalculationServices()
                .AddRemoteMonitoringServices()
                .AddFieldServiceManagementServices(configuration)
                .AddMechanicalAndOnStreamServices(configuration)
                .AddAPMServices(configuration)
                //.AddCRDServices(configuration)
                .AddPowerBIServices(configuration);


            // Emails (SendGrid)
            services.AddSendGrid(configuration);

            // Notifications
            services.AddSingleton<INotificationsService, NotificationsService>();

            // ZDapperPlus
            services.Configure<ZDapperPlus>(configuration.GetSection(nameof(ZDapperPlus)));
            services.Configure<Emails>(configuration.GetSection(nameof(Emails)));
            var provider = services.BuildServiceProvider();
            var zDapperPlus = (IOptions<ZDapperPlus>)provider.GetService(typeof(IOptions<ZDapperPlus>));
            DapperPlusManager.AddLicense(zDapperPlus.Value.LicenseName, zDapperPlus.Value.LicenseKey);

            // DeploymentEnvironment
            services.AddScoped(typeof(DeploymentEnvironment));

            services.AddHttpClient();

            return services;
        }

        /// <summary>
        ///     Add services that provide the ability to get Pipeline Inspection data
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        //public static IServiceCollection AddPipelineInspectionServices(this IServiceCollection services,
        //    IConfiguration configuration)
        //{
        //    if (configuration == null) throw new ArgumentNullException(nameof(configuration));

        //    var piaWebAPIManager = new WebAPIManager();

        //    piaWebAPIManager.Initialize(configuration.GetConnectionString("PipelineInspection"));
        //    services.TryAddSingleton(piaWebAPIManager);


        //    return services;
        //}

        /// <summary>
        ///     Add services that provide the ability to get Flange Calculation data
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddFlangeCalculationServices(this IServiceCollection services)
        {
            services.AddScoped<ICalculationService, CalculationService>();

            return services;
        }

        /// <summary>
        ///     Add services that provide the ability to get sensor readings
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddRemoteMonitoringServices(this IServiceCollection services)
        {
            services.AddScoped<ISensorReadingsSQLService, SensorReadingsSQLService>();
            return services;
        }

        /// <summary>
        ///     Register the Swagger generator, defining 1 or more Swagger documents
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddSwaggerDocumentation(this IServiceCollection services)
        {
            services.AddSwaggerGen(c =>
            {
                c.CustomSchemaIds(type => type.ToString());

                var scheme = new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.Http,
                    BearerFormat = "JWT",
                    Scheme = "bearer",
                    In = ParameterLocation.Header
                };
                c.AddSecurityDefinition("bearer", scheme);
                c.OperationFilter<AddAuthHeaderOperationFilter>();

                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Version = "v1",
                    Title = "Client Portal API",
                    Description = "API facilitating WMO and EDR feature modules"
                });

                // Set the comments path for the Swagger JSON and UI.

                // OrderTracking.API xml comments:
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                c.IncludeXmlComments(xmlPath);
            });

            services.AddSwaggerGenNewtonsoftSupport(); // explicit opt-in - needs to be placed after AddSwaggerGen()

            return services;
        }
    }    /// <summary>
    /// Cosmos DB client adapter
    /// </summary>
    public class CosmosClientAdapter : ICosmosClientAdapter
    {
        private readonly ValidatingCosmosClient _client;

        /// <summary>
        /// Initializes a new instance of the CosmosClientAdapter
        /// </summary>
        /// <param name="client">The Cosmos client</param>
        public CosmosClientAdapter(ValidatingCosmosClient client)
        {
            _client = client;
        }

        /// <summary>
        /// Gets the Cosmos client
        /// </summary>
        /// <returns>The Cosmos client</returns>
        public ValidatingCosmosClient GetClient() => _client;

        /// <summary>
        /// Gets a Cosmos container
        /// </summary>
        /// <param name="databaseName">Database name</param>
        /// <param name="containerName">Container name</param>
        /// <returns>The Cosmos container</returns>
        public Container GetContainer(string databaseName, string containerName) =>
            _client.GetContainer(databaseName, containerName);
    }

    /// <summary>
    /// Interface for Cosmos DB client adapter
    /// </summary>
    public interface ICosmosClientAdapter
    {
        /// <summary>
        /// Gets the Cosmos client
        /// </summary>
        /// <returns>The Cosmos client</returns>
        ValidatingCosmosClient GetClient();
        
        /// <summary>
        /// Gets a Cosmos container
        /// </summary>
        /// <param name="databaseName">Database name</param>
        /// <param name="containerName">Container name</param>
        /// <returns>The Cosmos container</returns>
        Container GetContainer(string databaseName, string containerName);
    }

    /// <summary>
    ///     Swashbuckle operation filter required to get bearer token authentication to work for api documentation pages
    /// </summary>
    public class AddAuthHeaderOperationFilter : IOperationFilter
    {
        /// <summary>
        ///     Applies the operation filter
        /// </summary>
        /// <param name="operation"></param>
        /// <param name="context"></param>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (operation == null) throw new ArgumentNullException(nameof(operation));

            operation.Security ??= new List<OpenApiSecurityRequirement>();

            var scheme = new OpenApiSecurityScheme
            { Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "bearer" } };
            operation.Security.Add(new OpenApiSecurityRequirement { [scheme] = new List<string>() });
        }
    }
}