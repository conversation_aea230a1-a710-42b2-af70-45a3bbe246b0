﻿using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;

namespace OrderTracking.API.Helpers
{
    /// <summary>
    /// Azure Key Vault Helper for retrieving secrets
    /// </summary>
    public class CloudStorageServiceHelper
    {
        private static SecretClient _secretClient;
        private static readonly object _lock = new object();

        /// <summary>
        /// Get secret from Azure Key Vault
        /// </summary>
        /// <param name="keyVaultUri">Azure Key Vault URI</param>
        /// <param name="secretName">Secret name</param>
        /// <returns>Secret value</returns>
        public static async Task<string> GetSecretAsync(string keyVaultUri, string secretName)
        {
            try
            {
                var client = GetSecretClient(keyVaultUri);
                var secret = await client.GetSecretAsync(secretName);
                return secret.Value.Value;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to retrieve secret '{secretName}' from Azure Key Vault: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get secret synchronously (for backward compatibility)
        /// </summary>
        /// <param name="keyVaultUri">Azure Key Vault URI</param>
        /// <param name="secretName">Secret name</param>
        /// <returns>Secret value</returns>
        public static string GetSecret(string keyVaultUri, string secretName)
        {
            return GetSecretAsync(keyVaultUri, secretName).GetAwaiter().GetResult();
        }

        private static SecretClient GetSecretClient(string keyVaultUri)
        {
            if (_secretClient == null)
            {
                lock (_lock)
                {
                    if (_secretClient == null)
                    {
                        var credential = new DefaultAzureCredential();
                        _secretClient = new SecretClient(new Uri(keyVaultUri), credential);
                    }
                }
            }
            return _secretClient;
        }
    }
}
