﻿using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using System;
using System.Threading.Tasks;

public class AzureKeyVaultHelper
{
    private static SecretClient _secretClient;
    private static readonly object _lock = new object();

    private static SecretClient GetSecretClient(string keyVaultUri)
    {
        if (_secretClient == null)
        {
            lock (_lock)
            {
                if (_secretClient == null)
                {
                    var credential = new DefaultAzureCredential();
                    _secretClient = new SecretClient(new Uri(keyVaultUri), credential);
                }
            }
        }
        return _secretClient;
    }

    public static async Task<string> GetSecretAsync(string secretName, string keyVaultUri)
    {
        try
        {
            var client = GetSecretClient(keyVaultUri);
            var secret = await client.GetSecretAsync(secretName);
            return secret.Value.Value;
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to retrieve secret '{secretName}' from Azure Key Vault: {ex.Message}", ex);
        }
    }

    public static string GetSecret(string secretName, string keyVaultUri)
    {
        return GetSecretAsync(secretName, keyVaultUri).GetAwaiter().GetResult();
    }
}


