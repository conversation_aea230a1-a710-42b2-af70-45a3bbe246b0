using System;

namespace OrderTracking.API.Models
{
    /// <summary>
    /// Cloud Storage Object model for compatibility with existing interfaces
    /// </summary>
    public class CloudStorageObject
    {
        /// <summary>
        /// Name of the object/blob
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Size of the object in bytes
        /// </summary>
        public long? Size { get; set; }

        /// <summary>
        /// Content type of the object
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// Last updated timestamp
        /// </summary>
        public DateTime? Updated { get; set; }

        /// <summary>
        /// ETag of the object
        /// </summary>
        public string ETag { get; set; }

        /// <summary>
        /// Bucket/Container name
        /// </summary>
        public string Bucket { get; set; }
    }
}
