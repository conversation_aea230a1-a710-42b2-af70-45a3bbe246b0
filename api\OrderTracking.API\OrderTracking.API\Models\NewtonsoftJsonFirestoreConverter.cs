using System.IO;
using System.Text;
using Newtonsoft.Json;
// using Google.Cloud.Firestore;
using System.Collections.Generic;

namespace OrderTracking.API.Models
{
    public class NewtonsoftJsonFirestoreConverter
    {
        private static readonly Encoding DefaultEncoding = new UTF8Encoding(false, true);
        private readonly JsonSerializer _serializer;

        public NewtonsoftJsonFirestoreConverter(JsonSerializerSettings settings)
        {
            _serializer = JsonSerializer.Create(settings);
        }

        // Converts an object of type T to a Firestore document represented as a Dictionary
        public Dictionary<string, object> ToFirestoreDocument<T>(T input)
        {
            var json = JsonConvert.SerializeObject(input, _serializer.Formatting);
            var dictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
            return dictionary ?? new Dictionary<string, object>();
        }

        // Converts a Firestore document (Dictionary) to an object of type T
        public T FromFirestoreDocument<T>(Dictionary<string, object> documentData)
        {
            var json = JsonConvert.SerializeObject(documentData);
            T result = JsonConvert.DeserializeObject<T>(json);
            return result;
        }

        // TODO: Remove this method - migrated to Cosmos DB
        // Deserialize from Firestore DocumentSnapshot
        // public T FromDocumentSnapshot<T>(DocumentSnapshot snapshot)
        // {
        //     if (!snapshot.Exists)
        //     {
        //         throw new FileNotFoundException("Document does not exist.");
        //     }

        //     var dictionary = snapshot.ToDictionary();
        //     return FromFirestoreDocument<T>(dictionary);
        // }

        // Optionally, add methods for working directly with streams if needed for compatibility
        public T FromStream<T>(Stream stream)
        {
            using var sr = new StreamReader(stream, DefaultEncoding);
            using var jsonTextReader = new JsonTextReader(sr);
            return _serializer.Deserialize<T>(jsonTextReader);
        }

        public Stream ToStream<T>(T input)
        {
            var streamPayload = new MemoryStream();
            using (var sw = new StreamWriter(streamPayload, DefaultEncoding, 1024, true))
            using (var jsonWriter = new JsonTextWriter(sw) { Formatting = _serializer.Formatting })
            {
                _serializer.Serialize(jsonWriter, input);
            }
            streamPayload.Position = 0;
            return streamPayload;
        }
    }
}
