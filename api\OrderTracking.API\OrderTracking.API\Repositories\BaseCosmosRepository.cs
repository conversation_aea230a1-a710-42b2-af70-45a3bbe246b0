using ClientPortal.Shared.Models;
using Microsoft.Azure.Cosmos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OrderTracking.API.Extensions;

namespace OrderTracking.API.Repositories
{
    public abstract class BaseCosmosRepository<TEntity, TKey> : IAsyncCosmosRepository<TEntity, TKey>
        where TEntity : ICosmosEntity<TKey>
    {
        protected Container Container { get; }

        protected BaseCosmosRepository(Container container)
        {
            Container = container ?? throw new ArgumentNullException(nameof(container));
        }

        public virtual async Task<TEntity> AddAsync(TEntity entity)
        {
            try
            {
                var response = await Container.CreateItemAsync(entity, new PartitionKey(entity.Id.ToString()));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
        {
            try
            {
                var query = new QueryDefinition("SELECT * FROM c");
                var iterator = Container.GetItemQueryIterator<TEntity>(query);
                var results = new List<TEntity>();

                while (iterator.HasMoreResults)
                {
                    var response = await iterator.ReadNextAsync();
                    results.AddRange(response);
                }

                return results;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task<TEntity> GetAsync(TKey entityId);

        public virtual async Task<TEntity> GetAsync(TKey id, string partitionId)
        {
            try
            {
                var response = await Container.ReadItemAsync<TEntity>(id.ToString(), new PartitionKey(partitionId));
                return response.Resource;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return default;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task RemoveAsync(TKey id)
        {
            try
            {
                await Container.DeleteItemAsync<TEntity>(id.ToString(), new PartitionKey(id.ToString()));
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // Item already doesn't exist, consider this a success
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task RemoveAsync(TKey id, string partitionId)
        {
            try
            {
                await Container.DeleteItemAsync<TEntity>(id.ToString(), new PartitionKey(partitionId));
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // Item already doesn't exist, consider this a success
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task RemoveAsync(TEntity entity)
        {
            try
            {
                await RemoveAsync(entity.Id);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task<TEntity> UpdateAsync(TEntity entity, TEntity originalEntity)
        {
            try
            {
                var response = await Container.UpsertItemAsync(entity, new PartitionKey(entity.Id.ToString()));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task<TEntity> UpdateAsync(TEntity entity, TKey originalId);

        public async Task<TEntity> UpdateAsync(TEntity entity)
        {
            try
            {
                var response = await Container.UpsertItemAsync(entity, new PartitionKey(entity.Id.ToString()));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
    }
}
