﻿using System;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration;

namespace OrderTracking.API.Repositories
{
    public sealed class ContainerFactory : IContainerFactory
    {
        #region Fields and Constants

        private readonly ValidatingCosmosClient _cosmosClient;
        // private readonly ValidatingFirestoreClient _firestoreClient; // For backward compatibility
        private readonly string _database;

        private readonly string _rolesContainer;

        private readonly string _usersContainer;

        #endregion

        #region Constructors

        public ContainerFactory(IConfigurationSection configurationSection, ValidatingCosmosClient cosmosClient)
        {
            if (configurationSection == null) throw new ArgumentNullException(nameof(configurationSection));

            //Get the database name and override defaults if appropriate
            _database = configurationSection.GetSection("Database").Value;
            _rolesContainer = configurationSection.GetSection("Roles").Value;
            _usersContainer = configurationSection.GetSection("UserProfiles").Value;

            _cosmosClient = cosmosClient ?? throw new ArgumentNullException(nameof(cosmosClient));
            // _firestoreClient = firestoreClient; // Optional for backward compatibility
        }

        #endregion

        #region Interface Implementation

        public Container CreateContainer<T>(out string partitionKeyPath) where T : ICosmosRepository
        {
            var t = typeof(T);
            if (typeof(IRolesRepository).IsAssignableFrom(t))
            {
                partitionKeyPath = "/id"; // Cosmos DB partition key for roles
                return _cosmosClient.GetContainer(_database, _rolesContainer);
            }
            if (typeof(IUserProfileRepository).IsAssignableFrom(t))
            {
                partitionKeyPath = "/id"; // Cosmos DB partition key for user profiles
                return _cosmosClient.GetContainer(_database, _usersContainer);
            }

            partitionKeyPath = "/id";
            return null;
        }

        public Container CreateCollection<T>(out string partitionKeyPath) where T : IFirestoreRepository
        {
            // Legacy method for backward compatibility - now uses Cosmos DB
            var t = typeof(T);
            if (typeof(IRolesRepository).IsAssignableFrom(t))
            {
                partitionKeyPath = "/id";
                return _cosmosClient.GetContainer(_database, _rolesContainer);
            }
            if (typeof(IUserProfileRepository).IsAssignableFrom(t))
            {
                partitionKeyPath = "/id";
                return _cosmosClient.GetContainer(_database, _usersContainer);
            }

            partitionKeyPath = "/id";
            return null;
        }

        #endregion
    }
}