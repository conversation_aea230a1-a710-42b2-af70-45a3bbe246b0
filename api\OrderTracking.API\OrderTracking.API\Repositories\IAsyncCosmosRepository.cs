using ClientPortal.Shared.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    /// Async repository interface for Azure Cosmos DB operations
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <typeparam name="TKey">The key type</typeparam>
    public interface IAsyncCosmosRepository<TEntity, in TKey> where TEntity : ICosmosEntity<TKey>
    {
        /// <summary>
        /// Add a new entity
        /// </summary>
        /// <param name="entity">The entity to add</param>
        /// <returns>The added entity</returns>
        Task<TEntity> AddAsync(TEntity entity);

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>All entities</returns>
        Task<IEnumerable<TEntity>> GetAllAsync();

        /// <summary>
        /// Get an entity by its ID
        /// </summary>
        /// <param name="entityId">The entity ID</param>
        /// <returns>The entity if found, null otherwise</returns>
        Task<TEntity> GetAsync(TKey entityId);

        /// <summary>
        /// Get an entity by its ID and partition key
        /// </summary>
        /// <param name="id">The entity ID</param>
        /// <param name="partitionId">The partition key</param>
        /// <returns>The entity if found, null otherwise</returns>
        Task<TEntity> GetAsync(TKey id, string partitionId);

        /// <summary>
        /// Remove an entity by its ID
        /// </summary>
        /// <param name="id">The entity ID</param>
        Task RemoveAsync(TKey id);

        /// <summary>
        /// Remove an entity by its ID and partition key
        /// </summary>
        /// <param name="id">The entity ID</param>
        /// <param name="partitionId">The partition key</param>
        Task RemoveAsync(TKey id, string partitionId);

        /// <summary>
        /// Remove an entity
        /// </summary>
        /// <param name="entity">The entity to remove</param>
        Task RemoveAsync(TEntity entity);

        /// <summary>
        /// Update an entity
        /// </summary>
        /// <param name="entity">The updated entity</param>
        /// <param name="originalEntity">The original entity</param>
        /// <returns>The updated entity</returns>
        Task<TEntity> UpdateAsync(TEntity entity, TEntity originalEntity);

        /// <summary>
        /// Update an entity with a different ID
        /// </summary>
        /// <param name="entity">The updated entity</param>
        /// <param name="originalId">The original entity ID</param>
        /// <returns>The updated entity</returns>
        Task<TEntity> UpdateAsync(TEntity entity, TKey originalId);

        /// <summary>
        /// Update an entity
        /// </summary>
        /// <param name="entity">The entity to update</param>
        /// <returns>The updated entity</returns>
        Task<TEntity> UpdateAsync(TEntity entity);
    }
}
