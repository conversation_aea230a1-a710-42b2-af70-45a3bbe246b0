﻿using Microsoft.Azure.Cosmos;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    ///     Container Factory interface for Azure Cosmos DB
    /// </summary>
    public interface IContainerFactory
    {
        #region Public Methods

        /// <summary>
        ///     Create Cosmos DB container instance
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="partitionKeyPath"></param>
        /// <returns></returns>
        Container CreateContainer<T>(out string partitionKeyPath) where T : ICosmosRepository;

        /// <summary>
        ///     Create Cosmos DB container instance for legacy Firestore repositories
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="partitionKeyPath"></param>
        /// <returns></returns>
        Container CreateCollection<T>(out string partitionKeyPath) where T : IFirestoreRepository;

        #endregion
    }
}