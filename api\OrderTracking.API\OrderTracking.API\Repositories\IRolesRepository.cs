using System.Collections.Generic;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    ///     Repository for roles (seems to be in progress...)
    /// </summary>
    public interface IRolesRepository : IAsyncCosmosRepository<Role, string>
    {
        #region Public Methods

        /// <summary>
        ///     Get all roles for a specific group
        /// </summary>
        /// <param name="group"></param>
        /// <returns></returns>
        Task<IEnumerable<Role>> GetRolesForGroupAsync(string group);

        #endregion
    }
}