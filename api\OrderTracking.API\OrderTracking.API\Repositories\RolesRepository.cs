﻿using ClientPortal.Shared.Models;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderTracking.API.Repositories
{
    public class RolesRepository : BaseCosmosRepository<Role, string>, IRolesRepository, ICosmosRepository
    {
        private string _partitionProperty;

        #region Constructors

        public RolesRepository(IContainerFactory containerFactory, IConfiguration configuration)
            : base(CreateCosmosContainer(containerFactory))
        {
            var container = containerFactory.CreateContainer<RolesRepository>(out var partitionPropertyPath);
            SetPartitionPropertyFromPath(partitionPropertyPath);
        }

        private static Container CreateCosmosContainer(IContainerFactory containerFactory)
        {
            return containerFactory.CreateContainer<RolesRepository>(out _);
        }

        private void SetPartitionPropertyFromPath(string path)
        {
            if (path == null) throw new ArgumentNullException(nameof(path));

            // Split the path and set partition property based on some logic
            var pathSegments = path.Split('/');
            _partitionProperty = pathSegments.LastOrDefault();
        }

        public RolesRepository(Container container)
            : base(container)
        {
        }

        #endregion

        #region Interface Implementation

        public override async Task<Role> GetAsync(string entityId) => await GetAsync(entityId, entityId);

        public override async Task<Role> AddAsync(Role entity)
        {
            try
            {
                var response = await Container.CreateItemAsync(entity, new PartitionKey(entity.Id));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<IEnumerable<Role>> GetRolesForGroupAsync(string group)
        {
            try
            {
                var query = new QueryDefinition("SELECT * FROM c WHERE c.group = @group")
                    .WithParameter("@group", group);

                var iterator = Container.GetItemQueryIterator<Role>(query);
                var roles = new List<Role>();

                while (iterator.HasMoreResults)
                {
                    var response = await iterator.ReadNextAsync();
                    roles.AddRange(response);
                }

                return roles;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public override async Task RemoveAsync(string id) => await base.RemoveAsync(id);

        //update with ID change
        public async Task<Role> UpsertAsync(Role entity, string originalId)
        {
            var newRole = await AddAsync(entity); //adds new role
            await base.RemoveAsync(originalId);   //removes original role
            return newRole;
        }

        public override async Task<Role> UpdateAsync(Role entity, string originalId) =>
            await UpdateAsync(entity, originalId);

        public override Task RemoveAsync(string id, string partitionId)
        {
            return base.RemoveAsync(id, partitionId);
        }

        #endregion
    }
}