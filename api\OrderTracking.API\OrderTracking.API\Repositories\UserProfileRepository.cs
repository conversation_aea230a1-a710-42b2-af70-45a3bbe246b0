using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration;
// using OrderTracking.API.Extensions;

namespace OrderTracking.API.Repositories
{
    public class UserProfileRepository : BaseCosmosRepository<UserProfile, string>, IUserProfileRepository, ICosmosRepository
    {
        // private string _partitionProperty;
        #region Constructors

        public UserProfileRepository(IContainerFactory containerFactory, IConfiguration configuration) : base(CreateCosmosContainer(containerFactory))
        {
            var container = containerFactory.CreateContainer<UserProfileRepository>(out var partitionPropertyPath);
            SetPartitionPropertyFromPath(partitionPropertyPath);
        }

        private static Container CreateCosmosContainer(IContainerFactory containerFactory)
        {
            return containerFactory.CreateContainer<UserProfileRepository>(out _);
        }

        private void SetPartitionPropertyFromPath(string path)
        {
            if (path == null) throw new ArgumentNullException(nameof(path));

            // Split the path and set partition property based on some logic
            // var pathSegments = path.Split('/');
            // _partitionProperty = pathSegments.LastOrDefault();
        }

        public UserProfileRepository(Container container)
            : base(container)
        {
        }

        #endregion

        #region Interface Implementation

        public override async Task<UserProfile> GetAsync(string entityId)
        {
            // Call the base class method to get the document snapshot
            var documentSnapshot = await GetAsync(entityId, entityId);
            return documentSnapshot;
        }

        public override async Task<UserProfile> AddAsync(UserProfile entity)
        {
            try
            {
                var response = await Container.CreateItemAsync(entity, new PartitionKey(entity.Id));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<IEnumerable<UserProfile>> GetUsersForGroupAsync(string group)
        {
            try
            {
                var query = new QueryDefinition("SELECT * FROM c");
                var iterator = Container.GetItemQueryIterator<UserProfile>(query);
                var filteredUserProfiles = new List<UserProfile>();

                while (iterator.HasMoreResults)
                {
                    var response = await iterator.ReadNextAsync();
                    foreach (var userProfile in response)
                    {
                        var roles = userProfile.Roles ?? new List<string>();
                        // Check if any role starts with the specified group
                        if (roles.Any(role => role.ToUpper().StartsWith(group.ToUpper())))
                            filteredUserProfiles.Add(userProfile);
                    }
                }

                return filteredUserProfiles;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<IEnumerable<UserProfile>> GetUsersForRoleAsync(string role)
        {
            try
            {
                var query = new QueryDefinition("SELECT * FROM c");
                var iterator = Container.GetItemQueryIterator<UserProfile>(query);
                var filteredUserProfiles = new List<UserProfile>();

                while (iterator.HasMoreResults)
                {
                    var response = await iterator.ReadNextAsync();
                    foreach (var userProfile in response)
                    {
                        var roles = userProfile.Roles ?? new List<string>();
                        // Check if the role array contains the specified role
                        if (roles.Contains(role))
                            filteredUserProfiles.Add(userProfile);
                    }
                }

                return filteredUserProfiles;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public override async Task RemoveAsync(string id)
        {
            await base.RemoveAsync(id);
        }

        public override Task RemoveAsync(string id, string partitionId)
        {
            throw new System.NotImplementedException();
        }

        public override async Task<UserProfile> UpdateAsync(UserProfile profile, string originalId)
        {
            try
            {
                var response = await Container.UpsertItemAsync(profile, new PartitionKey(profile.Id));
                return response.Resource;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        #endregion
    }
}