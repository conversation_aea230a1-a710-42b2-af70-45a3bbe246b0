using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.Azure.Cosmos;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Service for managing authentication history using Azure Cosmos DB
    /// </summary>
    public class AuthHistoryService : IAuthHistoryService
    {
        private readonly Container _container;
        private readonly CosmosClient _cosmosClient;        /// <summary>
        /// Initializes a new instance of the AuthHistoryService
        /// </summary>
        /// <param name="options">Connection options</param>
        public AuthHistoryService(IOptions<Connections> options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            _cosmosClient = new CosmosClient(options.Value.CosmosConnectionString ?? 
                $"AccountEndpoint={options.Value.Endpoint};AccountKey={options.Value.AuthKey};");
            _container = _cosmosClient.GetContainer(
                options.Value.CosmosDatabaseName ?? options.Value.DatabaseName, 
                options.Value.AuthHistoryContainerName ?? options.Value.AuthHistory);
        }

        /// <summary>
        /// Gets all authentication history items
        /// </summary>
        /// <returns>Collection of change events</returns>
        public async Task<IEnumerable<ChangeEvent>> GetItemsAsync()
        {
            var queryDefinition = new QueryDefinition("SELECT * FROM c ORDER BY c.createdAt DESC");
            
            var items = new List<ChangeEvent>();
            using var feedIterator = _container.GetItemQueryIterator<ChangeEvent>(queryDefinition);
            
            while (feedIterator.HasMoreResults)
            {
                var response = await feedIterator.ReadNextAsync();
                items.AddRange(response.ToList());
            }

            return items;
        }

        /// <summary>
        /// Gets a specific authentication history item by ID
        /// </summary>
        /// <param name="id">The item ID</param>
        /// <returns>The change event or null if not found</returns>
        public async Task<ActionResult<ChangeEvent>> GetItemAsync(string id)
        {
            try
            {
                var response = await _container.ReadItemAsync<ChangeEvent>(id, new PartitionKey(id));
                return response.Resource;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return null;
            }
        }

        /// <summary>
        /// Adds a new authentication history item
        /// </summary>
        /// <param name="changeEvent">The change event to add</param>
        /// <returns>The ID of the created item</returns>
        public async Task<string> AddItemAsync(ChangeEvent changeEvent)
        {
            if (changeEvent == null) throw new ArgumentNullException(nameof(changeEvent));
            if (changeEvent.Old == null && changeEvent.New == null)
                throw new InvalidOperationException(
                    "Cannot create ChangeEvent without old and/or new snapshots of record being changed.");

            if (string.IsNullOrEmpty(changeEvent.Id))
                changeEvent.Id = Guid.NewGuid().ToString();            var response = await _container.CreateItemAsync(changeEvent, new PartitionKey(changeEvent.Id));
            // Item created successfully

            return response.Resource.Id;
        }

        /// <summary>
        /// Removes an authentication history item by ID
        /// </summary>
        /// <param name="id">The item ID</param>
        public async Task RemoveAsync(string id)
        {
            try
            {
                await _container.DeleteItemAsync<ChangeEvent>(id, new PartitionKey(id));
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // Item doesn't exist, nothing to delete
            }        }
    }
}
