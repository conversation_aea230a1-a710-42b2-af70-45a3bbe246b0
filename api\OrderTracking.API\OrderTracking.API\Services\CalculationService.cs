﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;
using Internal;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using OrderTracking.API.Extensions;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Models;
using System;

namespace OrderTracking.API.Services
{    /// <summary>
    ///     Service class for Calculations
    /// </summary>
    public class CalculationService : ICalculationService
    {
        private readonly Container _container;
        private readonly ILogger<CalculationService> _logger;

        /// <summary>
        ///     Constructs the CalculationService class
        ///     instance
        /// </summary>
        /// <param name="adapter">The Cosmos client adapter</param>
        /// <param name="options">Configuration options</param>
        /// <param name="logger">Logger instance</param>
        public CalculationService(ICosmosClientAdapter adapter, IOptions<Connections> options, ILogger<CalculationService> logger)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            _container = adapter.GetContainer(options.Value.CosmosDatabaseName, options.Value.FlangeCalculations);
            _logger = logger;
        }        /// <summary>
        ///     Add an calculation
        /// </summary>
        /// <param name="calculation"></param>
        /// <returns></returns>
        public async Task<string> AddItemAsync(JObject calculation)
        {
            if (calculation == null) throw new ArgumentNullException(nameof(calculation));

            var calculationObject = calculation.ToObject<Dictionary<string, object>>();

            try
            {
                if (calculationObject["tighteningMethods"] != null)
                {
                    JArray jArray = (JArray)calculationObject["tighteningMethods"];
                    calculationObject["tighteningMethods"] = jArray.Select(x => x.ToString());
                }
                if (calculationObject["toolList"] != null)
                {
                    JArray jArray = (JArray)calculationObject["toolList"];
                    calculationObject["toolList"] = jArray.Select(x => x.ToString());
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occured while trying to cast Enumerable from JArray");
                throw;
            }

            // Generate a unique ID for the document
            var documentId = Guid.NewGuid().ToString();
            calculationObject["id"] = documentId;

            var response = await _container.CreateItemAsync(calculationObject, new PartitionKey(documentId));
            return response.Resource["id"].ToString();
        }        /// <summary>
        ///     Delete an calculation
        /// </summary>
        /// <param name="id"></param>
        /// <param name="pk"></param>
        /// <returns></returns>
        public async Task DeleteItemAsync(string id, string pk)
        {
            await _container.DeleteItemAsync<dynamic>(id, new PartitionKey(pk ?? id));
        }

        /// <summary>
        ///     Delete multiple calculations
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteItemsAsync(string[] ids)
        {
            if (ids == null) throw new ArgumentNullException(nameof(ids));

            foreach (var id in ids)
            {
                await _container.DeleteItemAsync<dynamic>(id, new PartitionKey(id));
            }
        }

        /// <summary>
        ///     Get a calculation
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<dynamic> GetItemAsync(string id)
        {
            try
            {
                var response = await _container.ReadItemAsync<dynamic>(id, new PartitionKey(id));
                return response.Resource;
            }
            catch (CosmosException e) when (e.StatusCode == HttpStatusCode.NotFound)
            {
                return default;
            }
        }

        /// <summary>
        ///     Get multiple calculations
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<dynamic>> GetItemsAsync()
        {
            var query = new QueryDefinition("SELECT * FROM c");
            var iterator = _container.GetItemQueryIterator<dynamic>(query);
            var results = new List<dynamic>();

            while (iterator.HasMoreResults)
            {
                var response = await iterator.ReadNextAsync();
                results.AddRange(response);
            }

            return results;
        }        /// <summary>
        ///     Update an Calculation
        /// </summary>
        /// <param name="id"></param>
        /// <param name="calculation"></param>
        /// <returns></returns>
        public async Task UpdateItemAsync(string id, JObject calculation)
        {
            if (calculation == null) throw new ArgumentNullException(nameof(calculation));

            var calculationObject = calculation.ToObject<Dictionary<string, object>>();

            try
            {
                if (calculationObject["tighteningMethods"] != null)
                {
                    JArray jArray = (JArray)calculationObject["tighteningMethods"];
                    calculationObject["tighteningMethods"] = jArray.Select(x => x.ToString());
                }
                if (calculationObject["toolList"] != null)
                {
                    JArray jArray = (JArray)calculationObject["toolList"];
                    calculationObject["toolList"] = jArray.Select(x => x.ToString());
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occured while trying to cast Enumerable from JArray");
                throw;
            }

            calculationObject["id"] = id;
            await _container.UpsertItemAsync(calculationObject, new PartitionKey(id));
        }
    }
}