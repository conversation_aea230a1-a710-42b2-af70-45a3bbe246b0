﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Sas;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Cloud Storage Service
    /// This class contains operations related list. add, download, upload and delete objects
    /// </summary>
    public class CloudStorageService : ICloudStorageService
    {
        private readonly ILogger<CloudStorageService> _logger;
        private readonly IOptions<BlobStorage> _options;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="options"></param>
        /// <param name="logger"></param>
        public CloudStorageService(IOptions<BlobStorage> options, ILogger<CloudStorageService> logger)
        {
            _logger = logger;
            _options = options;
        }

        /// <summary>
        /// Get signed URL for accessing blobs in the container
        /// </summary>
        /// <returns></returns>
        public Task<string> GetSignedUrl()
        {
            try
            {
                var blobServiceClient = new BlobServiceClient(_options.Value.ConnectionString);
                var containerClient = blobServiceClient.GetBlobContainerClient(_options.Value.APMContainer);

                if (containerClient.CanGenerateSasUri)
                {
                    var sasBuilder = new BlobSasBuilder
                    {
                        BlobContainerName = _options.Value.APMContainer,
                        Resource = "c", // Container
                        ExpiresOn = DateTimeOffset.UtcNow.AddHours(1)
                    };
                    sasBuilder.SetPermissions(BlobContainerSasPermissions.Read | BlobContainerSasPermissions.List);

                    var sasUri = containerClient.GenerateSasUri(sasBuilder);
                    _logger.LogInformation("Generated SAS URL for container {ContainerName}", _options.Value.APMContainer);
                    return Task.FromResult(sasUri.ToString());
                }

                throw new InvalidOperationException("Cannot generate SAS URI for the container");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating signed URL for container {ContainerName}", _options.Value.APMContainer);
                throw;
            }
        }

        /// <summary>
        /// Get signed URL for accessing a specific blob
        /// </summary>
        /// <param name="objectName"></param>
        /// <returns></returns>
        public Task<string> GetSignedUrl(string objectName)
        {
            try
            {
                var blobServiceClient = new BlobServiceClient(_options.Value.ConnectionString);
                var containerClient = blobServiceClient.GetBlobContainerClient(_options.Value.APMContainer);
                var blobClient = containerClient.GetBlobClient(objectName);

                if (blobClient.CanGenerateSasUri)
                {
                    var sasBuilder = new BlobSasBuilder
                    {
                        BlobContainerName = _options.Value.APMContainer,
                        BlobName = objectName,
                        Resource = "b", // Blob
                        ExpiresOn = DateTimeOffset.UtcNow.AddHours(1)
                    };
                    sasBuilder.SetPermissions(BlobSasPermissions.Read);

                    var sasUri = blobClient.GenerateSasUri(sasBuilder);
                    _logger.LogInformation("Generated SAS URL for blob {ObjectName} in container {ContainerName}", objectName, _options.Value.APMContainer);
                    return Task.FromResult(sasUri.ToString());
                }

                throw new InvalidOperationException($"Cannot generate SAS URI for blob {objectName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating signed URL for blob {ObjectName}", objectName);
                throw;
            }
        }

        /// <summary>
        /// List blobs from the container
        /// </summary>
        /// <param name="folderName"></param>
        /// <returns></returns>
        public async Task<IEnumerable<CloudStorageObject>> ListObjectAsync(string folderName)
        {
            try
            {
                var blobServiceClient = new BlobServiceClient(_options.Value.ConnectionString);
                var containerClient = blobServiceClient.GetBlobContainerClient(_options.Value.APMWOContainer);
                var blobs = new List<CloudStorageObject>();

                var prefix = string.IsNullOrEmpty(folderName) ? "" : folderName + "/";

                await foreach (var blobItem in containerClient.GetBlobsAsync(prefix: prefix))
                {
                    // Convert Azure blob to CloudStorageObject for compatibility
                    var storageObject = new CloudStorageObject
                    {
                        Name = blobItem.Name,
                        Size = blobItem.Properties.ContentLength,
                        Updated = blobItem.Properties.LastModified?.DateTime,
                        ContentType = blobItem.Properties.ContentType,
                        ETag = blobItem.Properties.ETag?.ToString(),
                        Bucket = _options.Value.APMWOContainer
                    };
                    blobs.Add(storageObject);
                    _logger.LogInformation("Found blob: {BlobName}", blobItem.Name);
                }

                _logger.LogInformation("Listed {BlobCount} blobs from container {ContainerName}, folder - {FolderName}", blobs.Count, _options.Value.APMWOContainer, folderName);
                return blobs;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing blobs from container {ContainerName}, folder - {FolderName}", _options.Value.APMWOContainer, folderName);
                throw;
            }
        }

        /// <summary>
        /// Upload attachment to blob storage
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        public async Task<CloudStorageObject> UploadAttachmentObjectAsync(string folderName, IFormFile file)
        {
            try
            {
                if (file == null) throw new ArgumentNullException(nameof(file));

                var blobServiceClient = new BlobServiceClient(_options.Value.ConnectionString);
                var containerClient = blobServiceClient.GetBlobContainerClient(_options.Value.APMWOContainer);
                await containerClient.CreateIfNotExistsAsync(PublicAccessType.None);

                var blobName = string.IsNullOrEmpty(folderName) ? file.FileName : $"{folderName}/{file.FileName}";
                var blobClient = containerClient.GetBlobClient(blobName);

                using var fileStream = file.OpenReadStream();
                var blobHttpHeaders = new BlobHttpHeaders
                {
                    ContentType = file.ContentType
                };

                var uploadResult = await blobClient.UploadAsync(fileStream, new BlobUploadOptions
                {
                    HttpHeaders = blobHttpHeaders
                });

                _logger.LogInformation("Upload completed for container {ContainerName}, blob - {BlobName}", _options.Value.APMWOContainer, blobName);

                // Return compatible object
                return new CloudStorageObject
                {
                    Name = blobName,
                    Size = file.Length,
                    ContentType = file.ContentType,
                    Updated = DateTime.UtcNow,
                    Bucket = _options.Value.APMWOContainer
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file {FileName} to container {ContainerName}", file.FileName, _options.Value.APMWOContainer);
                throw;
            }
        }

        /// <summary>
        /// Delete blob from storage
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        public async Task DeleteObjectAsync(string folderName, string objectName)
        {
            try
            {
                var blobServiceClient = new BlobServiceClient(_options.Value.ConnectionString);
                var containerClient = blobServiceClient.GetBlobContainerClient(_options.Value.APMWOContainer);
                var blobName = string.IsNullOrEmpty(folderName) ? objectName : $"{folderName}/{Uri.EscapeDataString(objectName)}";
                var blobClient = containerClient.GetBlobClient(blobName);

                await blobClient.DeleteIfExistsAsync();
                _logger.LogInformation("Deleted blob {BlobName} from container {ContainerName}", blobName, _options.Value.APMWOContainer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting blob {ObjectName} from Azure Blob Storage", objectName);
                throw;
            }
        }

        /// <summary>
        /// Download blob from storage
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        public async Task<CloudStorageDownloadedObject> DownloadObjectAsync(string folderName, string objectName)
        {
            try
            {
                var blobServiceClient = new BlobServiceClient(_options.Value.ConnectionString);
                var containerClient = blobServiceClient.GetBlobContainerClient(_options.Value.APMWOContainer);
                var blobName = string.IsNullOrEmpty(folderName) ? objectName : $"{folderName}/{Uri.EscapeDataString(objectName)}";
                var blobClient = containerClient.GetBlobClient(blobName);

                var stream = new MemoryStream();
                var downloadResult = await blobClient.DownloadToAsync(stream);
                stream.Position = 0;

                var downloadedObject = new CloudStorageDownloadedObject
                {
                    Stream = stream,
                    Object = new CloudStorageObject
                    {
                        Name = blobName,
                        Size = stream.Length,
                        ContentType = downloadResult.Headers.ContentType,
                        Bucket = _options.Value.APMWOContainer
                    }
                };

                _logger.LogInformation("Downloaded blob {BlobName} from container {ContainerName}", blobName, _options.Value.APMWOContainer);
                return downloadedObject;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading blob {ObjectName} from Azure Blob Storage", objectName);
                throw;
            }
        }

    }
}
