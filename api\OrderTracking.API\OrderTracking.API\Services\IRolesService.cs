using System.Collections.Generic;
using System.Threading.Tasks;
using OrderTracking.API.Repositories;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service class to provide access to roles
    /// </summary>
    public interface IRolesService: IRolesRepository
    {
        #region Public Methods

        /// <summary>
        ///     Get role groups
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<string>> GetGroupsAsync();

        /// <summary>
        ///     Remove something.  Probably a role.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        new Task RemoveAsync(string id);

        #endregion
    }
}