using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using OrderTracking.API.Extensions;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{    /// <summary>
    ///     CosmosDB-based service class to CRUD release notes documents
    /// </summary>
    public class ReleaseNotesService : IReleaseNotesService
    {
        private readonly Container _container;

        /// <summary>
        ///     Constructor for the class
        /// </summary>
        /// <param name="adapter">The Cosmos client adapter</param>
        /// <param name="options">Configuration options</param>
        public ReleaseNotesService(ICosmosClientAdapter adapter, IOptions<Connections> options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            _container = adapter.GetContainer(options.Value.CosmosDatabaseName, options.Value.ReleaseNotes);
        }        /// <summary>
        ///     Get items ordered by createdAt, desc
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<ReleaseNotes>> GetItemsAsync()
        {
            var query = new QueryDefinition("SELECT * FROM c");
            var iterator = _container.GetItemQueryIterator<ReleaseNotes>(query);
            var results = new List<ReleaseNotes>();

            while (iterator.HasMoreResults)
            {
                var response = await iterator.ReadNextAsync();
                results.AddRange(response);
            }

            return results.OrderByDescending(r => r.CreatedAt);
        }

        /// <summary>
        ///     Create a release notes document in the database.
        /// </summary>
        /// <param name="releaseNotes"></param>
        /// <returns></returns>
        public async Task<ReleaseNotes> AddItemAsync(ReleaseNotes releaseNotes)
        {
            releaseNotes.Id = Guid.NewGuid().ToString();
            var response = await _container.CreateItemAsync(releaseNotes, new PartitionKey(releaseNotes.Id));
            return response.Resource;
        }

        /// <summary>
        ///     Delete a release notes document from the database.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task DeleteItemAsync(string id)
        {
            await _container.DeleteItemAsync<ReleaseNotes>(id, new PartitionKey(id));
        }

        /// <summary>
        ///     Update an existing release notes document in the database.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="releaseNotes"></param>
        /// <returns></returns>
        public async Task UpdateItemAsync(string id, ReleaseNotes releaseNotes)
        {
            if (releaseNotes == null) throw new ArgumentNullException(nameof(releaseNotes));
            releaseNotes.Id = id;
            await _container.UpsertItemAsync(releaseNotes, new PartitionKey(id));
        }

        /// <summary>
        ///     Get a single release notes document from the database.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ReleaseNotes> GetItemAsync(string id)
        {
            try
            {
                var response = await _container.ReadItemAsync<ReleaseNotes>(id, new PartitionKey(id));
                return response.Resource;
            }
            catch (CosmosException e) when (e.StatusCode == HttpStatusCode.NotFound)
            {
                return default;
            }
        }

        /// <summary>
        ///     Delete multiple release notes items from the database at once.
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteItemsAsync(string[] ids)
        {
            if (ids == null) throw new ArgumentNullException(nameof(ids));

            foreach (var id in ids)
            {
                await _container.DeleteItemAsync<ReleaseNotes>(id, new PartitionKey(id));
            }
        }

    }
}