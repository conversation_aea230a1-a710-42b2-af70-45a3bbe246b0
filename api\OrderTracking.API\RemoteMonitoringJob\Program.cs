using System;
using System.Threading.Tasks;
using ClientPortal.Shared.Models.MOS;
using ClientPortal.Shared.Services;
using FluentMigrator.Runner;
using Microsoft.ApplicationInsights.WorkerService;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RemoteMonitoringJob.Migrations;
using RemoteMonitoringJob.Services;

namespace RemoteMonitoringJob
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var host = Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {
                    // Services will be configured here
                })
                .Build();

            var serviceCollection = new ServiceCollection()
                .AddLogging(b => b.AddConsole())
                .AddSingleton<IConfiguration>(host.Services.GetService<IConfiguration>())
                .AddSingleton<IWorkerService, WorkerService>()
                .AddLogging(lb => lb.AddFluentMigratorConsole())
                .AddApplicationInsightsTelemetryWorkerService()
                .AddSingleton<ISensorReadingsSQLService, SensorReadingsSQLService>()
                .AddSingleton<ISmartpimsScraperService, SmartpimsScraperService>()
                .AddFluentMigratorCore()
                        .ConfigureRunner(rb => rb
                            // Add Sql Server support to FluentMigrator
                            .AddSqlServer()
                            // Set the connection string
                            .WithGlobalConnectionString(
                                host.Services.GetService<IConfiguration>().GetConnectionString("RemoteMonitoring"))
                            // Define the assembly containing the migrations
                            .ScanIn(typeof(AddSensorReadingsTable).Assembly).For.Migrations())
                .BuildServiceProvider();

            var logger = serviceCollection.GetService<ILoggerFactory>().CreateLogger<Program>();
            logger.LogDebug("Starting WorkerService Execution..");
            var workerService = serviceCollection.GetService<IWorkerService>();
            await workerService.DoAsync();
        }
    }
}