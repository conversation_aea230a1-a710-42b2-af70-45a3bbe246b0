﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ClientPortal.Shared.Models.RemoteMonitoring;
using ClientPortal.Shared.Services;
using CsvHelper;
using FluentMigrator.Runner;
// using Google.Cloud.Diagnostics.Common;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using RemoteMonitoringJob.CSV;

namespace RemoteMonitoringJob.Services
{
	public class WorkerService : IWorkerService
    {
        private readonly ILogger<WorkerService> _logger;
        private readonly IServiceScopeFactory _scopes;
        private readonly ISmartpimsScraperService _scraper;
        private readonly ISensorReadingsSQLService _sqlService;
        private readonly IManagedTracer _managedTracer;

        public WorkerService(
            ILogger<WorkerService> logger,
            IServiceScopeFactory scopes,
            ISmartpimsScraperService scraper,
            ISensorReadingsSQLService sqlService,
            IManagedTracer managedTracer
)
        {
            _logger = logger;
            _scopes = scopes;
            _scraper = scraper;
            _sqlService = sqlService;
            _managedTracer = managedTracer;
        }

        public async Task DoAsync()
        {
            _logger.LogInformation("Timed Hosted Service running.");

            _logger.LogInformation("Running any outstanding database migrations");

            using (_managedTracer.StartSpan("Updating database"))
            {
                // IMigrationRunner from FluentMigrator is a scoped service.  But this IHostedService
                // is a singleton.  Because of that, we need to create a scope manually to get an
                // instance of an IMigrationRunner.  If you just inject IMigrationRunner into the
                // constructor of this class, an exception is thrown.  Please see the following URL:
                // https://www.thecodebuzz.com/cannot-consume-scoped-service-from-singleton-ihostedservice/
                using var scope = _scopes.CreateScope();
                var migrationRunner = scope.ServiceProvider.GetRequiredService<IMigrationRunner>();
                migrationRunner.MigrateUp();
            }

            await DoWork();
        }

        private async Task DoWork()
        {
            IEnumerable<string> ids;
            using (_managedTracer.StartSpan("Scrape smartpims for sensor ids"))
            {
                // Scrape the sensor ids
                ids = await _scraper.GetSensorIdsAsync();
            }

            if (ids == null) return;

            foreach (var id in ids)
            {
                _logger.LogInformation($"Downloading data for id: {id}");

                var sensorReadings = await DownloadAndParseSensorReadings(id);

                if (!sensorReadings.Any()) continue;

                _logger.LogInformation($"Saving readings for sensor with id: {id}");

                SaveSensorReadingsToDB(sensorReadings);
            }

            _logger.LogInformation("Run complete");
        }

        private void SaveSensorReadingsToDB(IEnumerable<SensorReading> sensorReadings)
        {
            using (_managedTracer.StartSpan("Save sensor readings to database"))
            {
                // Merge sensor readings
                _sqlService.MergeSensorReadings(sensorReadings);
            }
        }

        private async Task<List<SensorReading>> DownloadAndParseSensorReadings(string id)
        {
            List<SensorReading> sensorReadings;
            using (_managedTracer.StartSpan("Download and parse sensor readings"))
            {
                // Download the csv for the given sensor id
                var csvText = await _scraper.GetCSVTextAsync(id);

                using var reader = new StringReader(csvText);
                using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);

                // Map the C# class to the structure of the csv
                csv.Context.RegisterClassMap<SensorReadingMap>();

                // Parse csv
                sensorReadings = csv.GetRecords<SensorReading>().ToList();
            }

            return sensorReadings;
        }
    }
}

